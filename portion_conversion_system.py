#!/usr/bin/env python3
"""
基于官方辅食添加要点的分量转换系统
将成人餐食数据按照婴幼儿实际辅食摄入量进行科学转换
"""

import json
import pandas as pd
from typing import Dict, Any, Tuple

class InfantPortionConverter:
    """婴幼儿辅食分量转换器"""
    
    def __init__(self):
        # 基于官方辅食添加要点的分量标准
        self.portion_standards = {
            "6m_start": {
                "name": "6个月开始",
                "portion_description": "从尝一两口开始，逐渐增加到2～3小勺",
                "volume_ml": 15,  # 约2-3小勺 = 15ml
                "frequency": "从1次开始，逐渐推进到2次",
                "main_nutrition": "母乳为主，辅食为补充"
            },
            "6-9m": {
                "name": "6～9月龄", 
                "portion_description": "每餐2～3勺，逐渐增加到1/2碗（250ml的碗）",
                "volume_ml": 125,  # 1/2碗(250ml) = 125ml
                "frequency": "逐渐推进（半）固体食物摄入到1～2次",
                "main_nutrition": "母乳为主，辅食逐渐增加"
            },
            "9-12m": {
                "name": "9～12月龄",
                "portion_description": "1/2碗（250ml的碗）", 
                "volume_ml": 125,  # 1/2碗(250ml) = 125ml
                "frequency": "逐渐推进（半）固体食物摄入到2～3次",
                "main_nutrition": "母乳继续，辅食成为重要补充"
            },
            "12-24m": {
                "name": "12～24月龄",
                "portion_description": "3/4碗到1整碗（250ml的碗）",
                "volume_ml": 200,  # 平均约200ml (3/4碗到1整碗)
                "frequency": "3次家庭食物进餐 + 2次加餐",
                "main_nutrition": "继续母乳喂养，家庭食物为主"
            }
        }
        
        # 营养密度调整系数（考虑到婴幼儿需要更高的营养密度）
        self.nutrition_density_factors = {
            "6m_start": 1.5,   # 初期辅食需要更高营养密度
            "6-9m": 1.3,       # 适应期需要较高营养密度
            "9-12m": 1.2,      # 进阶期营养密度适中提高
            "12-24m": 1.1      # 幼儿期接近成人营养密度
        }
    
    def convert_adult_portion_to_infant(self, adult_meal_data: Dict, age_group: str) -> Dict[str, Any]:
        """将成人餐食分量转换为婴幼儿辅食分量"""
        
        if age_group not in self.portion_standards:
            raise ValueError(f"不支持的年龄组: {age_group}")
        
        # 获取年龄段标准
        age_standard = self.portion_standards[age_group]
        target_volume = age_standard["volume_ml"]
        
        # 估算成人餐食的体积（基于能量密度估算）
        # 一般成人餐食约1.5-2 kcal/ml，这里用1.8作为平均值
        estimated_adult_volume = adult_meal_data["energy"] / 1.8
        
        # 计算转换比例
        conversion_ratio = target_volume / estimated_adult_volume
        
        # 转换营养成分
        converted_nutrition = {
            "carb": adult_meal_data["carb"] * conversion_ratio,
            "protein": adult_meal_data["protein"] * conversion_ratio,
            "fat": adult_meal_data["fat"] * conversion_ratio,
            "energy": adult_meal_data["energy"] * conversion_ratio
        }
        
        # 应用营养密度调整系数
        density_factor = self.nutrition_density_factors[age_group]
        
        # 构建转换结果
        converted_data = {
            "original_adult_data": adult_meal_data,
            "age_group": age_group,
            "age_standard": age_standard,
            "conversion_info": {
                "estimated_adult_volume_ml": round(estimated_adult_volume, 1),
                "target_infant_volume_ml": target_volume,
                "conversion_ratio": round(conversion_ratio, 3),
                "density_adjustment_factor": density_factor
            },
            "converted_nutrition": {
                "carb_g": round(converted_nutrition["carb"], 2),
                "protein_g": round(converted_nutrition["protein"], 2), 
                "fat_g": round(converted_nutrition["fat"], 2),
                "energy_kcal": round(converted_nutrition["energy"], 1)
            },
            "feeding_guidance": self._generate_feeding_guidance(converted_nutrition, age_group),
            "nutrition_analysis": self._analyze_converted_nutrition(converted_nutrition, age_group)
        }
        
        return converted_data
    
    def _generate_feeding_guidance(self, nutrition_data: Dict, age_group: str) -> Dict[str, str]:
        """生成基于转换后营养数据的喂养指导"""
        age_standard = self.portion_standards[age_group]
        
        guidance = {
            "portion_size": age_standard["portion_description"],
            "frequency": age_standard["frequency"],
            "main_nutrition_source": age_standard["main_nutrition"],
            "preparation_tips": self._get_preparation_tips(age_group),
            "safety_reminders": self._get_safety_reminders(age_group)
        }
        
        return guidance
    
    def _get_preparation_tips(self, age_group: str) -> str:
        """获取食物制备建议"""
        tips = {
            "6m_start": "制作成稠糊状，用小勺喂食，质地要光滑无颗粒",
            "6-9m": "制作成糊糊状或粥烂状，可以有细小软烂颗粒",
            "9-12m": "切成细碎状，可以制作手指食物，鼓励自主抓取",
            "12-24m": "制作成软烂的家庭食物，鼓励使用餐具进食"
        }
        return tips.get(age_group, "根据月龄调整食物质地")
    
    def _get_safety_reminders(self, age_group: str) -> str:
        """获取安全提醒"""
        reminders = {
            "6m_start": "每次只添加一种新食物，观察3-5天；避免蜂蜜、坚果等",
            "6-9m": "注意食物温度适宜；避免小而硬的食物；观察过敏反应",
            "9-12m": "预防窒息，避免整颗葡萄、坚果；鼓励但监督自主进食",
            "12-24m": "确保食物充分咀嚼；建立规律进餐时间；避免边吃边玩"
        }
        return reminders.get(age_group, "注意食品安全和年龄适宜性")
    
    def _analyze_converted_nutrition(self, nutrition_data: Dict, age_group: str) -> Dict[str, str]:
        """分析转换后的营养数据"""
        
        # 获取年龄段营养需求参考值（辅食部分）
        complementary_food_needs = {
            "6m_start": {"energy": 50, "protein": 2},      # 辅食提供的营养很少
            "6-9m": {"energy": 150, "protein": 5},         # 辅食逐渐增加
            "9-12m": {"energy": 250, "protein": 8},        # 辅食成为重要补充
            "12-24m": {"energy": 400, "protein": 12}       # 辅食提供较多营养
        }
        
        needs = complementary_food_needs.get(age_group, {"energy": 200, "protein": 8})
        
        energy_ratio = (nutrition_data["energy"] / needs["energy"]) * 100
        protein_ratio = (nutrition_data["protein"] / needs["protein"]) * 100
        
        analysis = {
            "energy_assessment": self._assess_energy_level(energy_ratio),
            "protein_assessment": self._assess_protein_level(protein_ratio),
            "overall_evaluation": self._get_overall_evaluation(energy_ratio, protein_ratio, age_group),
            "recommendations": self._get_nutrition_recommendations(nutrition_data, age_group)
        }
        
        return analysis
    
    def _assess_energy_level(self, ratio: float) -> str:
        """评估能量水平"""
        if ratio < 50:
            return f"能量偏低({ratio:.1f}%)"
        elif ratio <= 120:
            return f"能量适宜({ratio:.1f}%)"
        else:
            return f"能量偏高({ratio:.1f}%)"
    
    def _assess_protein_level(self, ratio: float) -> str:
        """评估蛋白质水平"""
        if ratio < 60:
            return f"蛋白质偏低({ratio:.1f}%)"
        elif ratio <= 150:
            return f"蛋白质充足({ratio:.1f}%)"
        else:
            return f"蛋白质丰富({ratio:.1f}%)"
    
    def _get_overall_evaluation(self, energy_ratio: float, protein_ratio: float, age_group: str) -> str:
        """获取整体评价"""
        age_name = self.portion_standards[age_group]["name"]
        
        if energy_ratio >= 80 and energy_ratio <= 120 and protein_ratio >= 80:
            return f"营养搭配适合{age_name}宝宝的辅食需求"
        elif energy_ratio > 120:
            return f"单次辅食分量可能偏大，建议分次给予"
        elif energy_ratio < 60:
            return f"营养密度偏低，建议搭配其他营养丰富的食物"
        else:
            return f"基本适合{age_name}宝宝，可根据实际情况调整"
    
    def _get_nutrition_recommendations(self, nutrition_data: Dict, age_group: str) -> str:
        """获取营养建议"""
        recommendations = []
        
        # 基于蛋白质含量的建议
        if nutrition_data["protein"] < 2:
            recommendations.append("建议搭配蛋类、肉类或豆制品增加蛋白质")
        elif nutrition_data["protein"] > 8:
            recommendations.append("蛋白质含量丰富，有助于生长发育")
        
        # 基于脂肪含量的建议
        if nutrition_data["fat"] < 2:
            recommendations.append("可适当添加健康脂肪，如牛油果、橄榄油")
        elif nutrition_data["fat"] > 6:
            recommendations.append("脂肪含量适宜，支持大脑发育")
        
        # 基于年龄段的特殊建议
        if age_group in ["6m_start", "6-9m"]:
            recommendations.append("继续母乳喂养，辅食作为营养补充")
        elif age_group == "9-12m":
            recommendations.append("可以尝试更多质地和口味，培养咀嚼能力")
        elif age_group == "12-24m":
            recommendations.append("鼓励自主进食，建立良好饮食习惯")
        
        return "; ".join(recommendations) if recommendations else "营养搭配基本合理"

def demonstrate_portion_conversion():
    """演示分量转换功能"""
    print("🔄 婴幼儿辅食分量转换系统演示")
    print("=" * 60)
    
    converter = InfantPortionConverter()
    
    # 示例成人餐食数据
    adult_meals = [
        {
            "meal_description": "烤鸡胸肉配糙米和蒸西兰花",
            "carb": 45.2,
            "protein": 35.8,
            "fat": 8.5,
            "energy": 385.0,
            "country": "USA"
        },
        {
            "meal_description": "番茄鸡蛋面条",
            "carb": 52.3,
            "protein": 18.6,
            "fat": 12.4,
            "energy": 378.0,
            "country": "CHN"
        }
    ]
    
    age_groups = ["6m_start", "6-9m", "9-12m", "12-24m"]
    
    for i, adult_meal in enumerate(adult_meals, 1):
        print(f"\n📖 示例 {i}: {adult_meal['meal_description']}")
        print(f"成人分量营养: 能量{adult_meal['energy']}kcal, 蛋白质{adult_meal['protein']}g")
        print("-" * 50)
        
        for age_group in age_groups:
            try:
                converted = converter.convert_adult_portion_to_infant(adult_meal, age_group)
                
                print(f"\n【{converted['age_standard']['name']}】")
                print(f"  官方标准: {converted['age_standard']['portion_description']}")
                print(f"  转换比例: {converted['conversion_info']['conversion_ratio']:.3f}")
                print(f"  转换后营养: 能量{converted['converted_nutrition']['energy_kcal']}kcal, " +
                      f"蛋白质{converted['converted_nutrition']['protein_g']}g")
                print(f"  营养评估: {converted['nutrition_analysis']['overall_evaluation']}")
                print(f"  制备建议: {converted['feeding_guidance']['preparation_tips']}")
                
            except Exception as e:
                print(f"  转换失败: {e}")

def main():
    """主函数"""
    print("🍼 基于官方辅食添加要点的分量转换系统")
    print("=" * 80)
    
    print("📋 官方辅食分量标准:")
    converter = InfantPortionConverter()
    
    for age_key, standard in converter.portion_standards.items():
        print(f"\n• {standard['name']}")
        print(f"  分量: {standard['portion_description']}")
        print(f"  频次: {standard['frequency']}")
        print(f"  营养来源: {standard['main_nutrition']}")
    
    print("\n" + "=" * 80)
    demonstrate_portion_conversion()
    
    print(f"\n💡 核心价值:")
    print("✅ 基于官方标准的科学分量转换")
    print("✅ 考虑母乳为主、辅食为辅的营养结构")
    print("✅ 年龄段精准匹配的营养密度调整")
    print("✅ 实用的制备和喂养指导")

if __name__ == "__main__":
    main()
