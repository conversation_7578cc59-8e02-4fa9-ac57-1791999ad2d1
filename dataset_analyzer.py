#!/usr/bin/env python3
"""
NutriBench LLM数据集分析工具
用于分析生成的训练数据集的质量和分布
"""

import json
import pandas as pd
from collections import Counter, defaultdict

class DatasetAnalyzer:
    def __init__(self, dataset_path: str):
        """初始化分析器"""
        with open(dataset_path, 'r', encoding='utf-8') as f:
            self.dataset = json.load(f)
        
        self.df = pd.DataFrame(self.dataset)
        
    def basic_statistics(self):
        """基础统计信息"""
        print("=== 数据集基础统计 ===")
        print(f"总样本数: {len(self.dataset)}")
        
        # 按类型统计
        type_counts = Counter([item['metadata']['type'] for item in self.dataset])
        print(f"\n按对话类型分布:")
        for type_name, count in type_counts.items():
            print(f"  {type_name}: {count} ({count/len(self.dataset)*100:.1f}%)")
        
        # 按年龄组统计
        age_counts = Counter([item['metadata']['age_group'] for item in self.dataset])
        print(f"\n按年龄组分布:")
        for age_group, count in age_counts.items():
            print(f"  {age_group}: {count} ({count/len(self.dataset)*100:.1f}%)")
        
        # 按国家统计
        country_counts = Counter([item['metadata']['country'] for item in self.dataset])
        print(f"\n按国家分布:")
        for country, count in country_counts.items():
            print(f"  {country}: {count} ({count/len(self.dataset)*100:.1f}%)")
    
    def text_analysis(self):
        """文本分析"""
        print("\n=== 文本质量分析 ===")
        
        # 指令长度分析
        instruction_lengths = [len(item['instruction']) for item in self.dataset]
        output_lengths = [len(item['output']) for item in self.dataset]
        
        print(f"指令平均长度: {sum(instruction_lengths)/len(instruction_lengths):.1f} 字符")
        print(f"回答平均长度: {sum(output_lengths)/len(output_lengths):.1f} 字符")
        print(f"指令长度范围: {min(instruction_lengths)} - {max(instruction_lengths)}")
        print(f"回答长度范围: {min(output_lengths)} - {max(output_lengths)}")
        
        # 关键词分析
        self._analyze_keywords()
    
    def _analyze_keywords(self):
        """关键词分析"""
        print(f"\n关键营养词汇覆盖:")
        
        nutrition_keywords = {
            '营养成分': ['蛋白质', '碳水化合物', '脂肪', '能量', '维生素', '矿物质'],
            '年龄相关': ['个月', '岁', '婴儿', '幼儿', '宝宝'],
            '喂养指导': ['母乳', '辅食', '配方奶', '添加', '喂养'],
            '健康监测': ['生长', '发育', '体重', '身高', '营养不良'],
            '食物安全': ['过敏', '卫生', '新鲜', '温度', '质地']
        }
        
        all_text = ' '.join([item['instruction'] + ' ' + item['output'] for item in self.dataset])
        
        for category, keywords in nutrition_keywords.items():
            found_keywords = [kw for kw in keywords if kw in all_text]
            coverage = len(found_keywords) / len(keywords) * 100
            print(f"  {category}: {coverage:.1f}% ({len(found_keywords)}/{len(keywords)})")
    
    def quality_assessment(self):
        """质量评估"""
        print("\n=== 数据质量评估 ===")
        
        # 检查重复内容
        instructions = [item['instruction'] for item in self.dataset]
        unique_instructions = set(instructions)
        duplicate_rate = (len(instructions) - len(unique_instructions)) / len(instructions) * 100
        print(f"指令重复率: {duplicate_rate:.1f}%")
        
        # 检查空内容
        empty_instructions = sum(1 for item in self.dataset if not item['instruction'].strip())
        empty_outputs = sum(1 for item in self.dataset if not item['output'].strip())
        print(f"空指令数: {empty_instructions}")
        print(f"空回答数: {empty_outputs}")
        
        # 检查格式一致性
        self._check_format_consistency()
    
    def _check_format_consistency(self):
        """检查格式一致性"""
        print(f"\n格式一致性检查:")
        
        # 检查必需字段
        required_fields = ['instruction', 'output', 'metadata']
        missing_fields = defaultdict(int)
        
        for item in self.dataset:
            for field in required_fields:
                if field not in item:
                    missing_fields[field] += 1
        
        if missing_fields:
            print("  缺失字段:")
            for field, count in missing_fields.items():
                print(f"    {field}: {count} 个样本")
        else:
            print("  ✓ 所有样本都包含必需字段")
        
        # 检查metadata完整性
        metadata_fields = ['type', 'age_group', 'country']
        for field in metadata_fields:
            missing = sum(1 for item in self.dataset if field not in item.get('metadata', {}))
            if missing > 0:
                print(f"  metadata.{field} 缺失: {missing} 个样本")
    
    def diversity_analysis(self):
        """多样性分析"""
        print("\n=== 内容多样性分析 ===")
        
        # 年龄组 x 对话类型的分布
        age_type_matrix = defaultdict(lambda: defaultdict(int))
        for item in self.dataset:
            age = item['metadata']['age_group']
            type_name = item['metadata']['type']
            age_type_matrix[age][type_name] += 1
        
        print("年龄组 x 对话类型分布:")
        types = set()
        for age_dict in age_type_matrix.values():
            types.update(age_dict.keys())
        
        print(f"{'年龄组':<10}", end="")
        for t in sorted(types):
            print(f"{t:<20}", end="")
        print()
        
        for age in sorted(age_type_matrix.keys()):
            print(f"{age:<10}", end="")
            for t in sorted(types):
                count = age_type_matrix[age][t]
                print(f"{count:<20}", end="")
            print()
    
    def generate_report(self, output_path: str = "dataset_analysis_report.txt"):
        """生成分析报告"""
        import sys
        from io import StringIO
        
        # 重定向输出到字符串
        old_stdout = sys.stdout
        sys.stdout = buffer = StringIO()
        
        try:
            self.basic_statistics()
            self.text_analysis()
            self.quality_assessment()
            self.diversity_analysis()
        finally:
            sys.stdout = old_stdout
        
        report_content = buffer.getvalue()
        
        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# NutriBench LLM数据集分析报告\n\n")
            f.write(f"生成时间: {pd.Timestamp.now()}\n\n")
            f.write(report_content)
        
        print(f"分析报告已保存到: {output_path}")
        return report_content
    
    def export_samples(self, sample_size: int = 10, output_path: str = "dataset_samples.json"):
        """导出样本数据"""
        import random
        
        samples = random.sample(self.dataset, min(sample_size, len(self.dataset)))
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(samples, f, ensure_ascii=False, indent=2)
        
        print(f"已导出 {len(samples)} 个样本到: {output_path}")
    
    def validate_nutrition_content(self):
        """验证营养内容的专业性"""
        print("\n=== 营养内容专业性验证 ===")
        
        # 检查营养素数值的合理性
        nutrition_issues = []
        
        for i, item in enumerate(self.dataset):
            output = item['output']
            
            # 检查是否包含营养建议
            if '营养建议' not in output and item['metadata']['type'] == 'nutrition_analysis':
                nutrition_issues.append(f"样本 {i}: 缺少营养建议")
            
            # 检查是否包含年龄适宜性评估
            if '年龄' not in output and 'age_group' in item['metadata']:
                nutrition_issues.append(f"样本 {i}: 缺少年龄适宜性评估")
        
        if nutrition_issues:
            print(f"发现 {len(nutrition_issues)} 个潜在问题:")
            for issue in nutrition_issues[:5]:  # 只显示前5个
                print(f"  {issue}")
            if len(nutrition_issues) > 5:
                print(f"  ... 还有 {len(nutrition_issues) - 5} 个问题")
        else:
            print("✓ 营养内容专业性检查通过")

def main():
    """主函数"""
    analyzer = DatasetAnalyzer('nutribench_llm_dataset.json')
    
    # 运行所有分析
    analyzer.basic_statistics()
    analyzer.text_analysis()
    analyzer.quality_assessment()
    analyzer.diversity_analysis()
    analyzer.validate_nutrition_content()
    
    # 生成报告
    analyzer.generate_report()
    
    # 导出样本
    analyzer.export_samples(sample_size=5)

if __name__ == "__main__":
    main()
