#!/usr/bin/env python3
"""
数据质量控制和人工审核系统
用于审核LLM生成的营养指导对话数据，确保专业性和准确性
"""

import json
import pandas as pd
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import streamlit as st
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ReviewCriteria:
    """审核标准"""
    accuracy: int = 0  # 准确性 (1-5分)
    completeness: int = 0  # 完整性 (1-5分)
    clarity: int = 0  # 清晰度 (1-5分)
    safety: int = 0  # 安全性 (1-5分)
    age_appropriateness: int = 0  # 年龄适宜性 (1-5分)
    overall_score: float = 0.0
    comments: str = ""
    approved: bool = False
    reviewer_id: str = ""
    review_date: str = ""

@dataclass
class ReviewResult:
    """审核结果"""
    conversation_id: str
    criteria: ReviewCriteria
    suggested_improvements: List[str]
    action: str  # "approve", "reject", "revise"

class QualityController:
    """质量控制器"""
    
    def __init__(self, dataset_path: str):
        self.dataset_path = dataset_path
        self.dataset = self._load_dataset()
        self.reviews = self._load_reviews()
        self.review_guidelines = self._load_review_guidelines()
    
    def _load_dataset(self) -> List[Dict[str, Any]]:
        """加载数据集"""
        try:
            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"数据集文件不存在: {self.dataset_path}")
            return []
    
    def _load_reviews(self) -> Dict[str, ReviewResult]:
        """加载已有的审核结果"""
        review_path = self.dataset_path.replace('.json', '_reviews.json')
        try:
            with open(review_path, 'r', encoding='utf-8') as f:
                reviews_data = json.load(f)
                return {
                    r['conversation_id']: ReviewResult(**r) 
                    for r in reviews_data
                }
        except FileNotFoundError:
            return {}
    
    def _load_review_guidelines(self) -> Dict[str, Any]:
        """加载审核指南"""
        return {
            "accuracy_guidelines": {
                "5": "营养信息完全准确，符合最新科学证据",
                "4": "营养信息基本准确，有轻微不精确",
                "3": "营养信息大体正确，有一些小错误",
                "2": "营养信息有明显错误，但不危险",
                "1": "营养信息严重错误，可能有害"
            },
            "completeness_guidelines": {
                "5": "回答全面完整，涵盖所有重要方面",
                "4": "回答较完整，遗漏少量次要信息",
                "3": "回答基本完整，遗漏一些重要信息",
                "2": "回答不够完整，遗漏较多信息",
                "1": "回答严重不完整，缺少关键信息"
            },
            "safety_guidelines": {
                "5": "完全符合安全标准，包含必要警告",
                "4": "基本安全，有轻微安全考虑不足",
                "3": "总体安全，有一些安全提醒缺失",
                "2": "有安全隐患，缺少重要安全提醒",
                "1": "存在严重安全风险"
            },
            "red_flags": [
                "建议6个月内婴儿食用固体食物",
                "推荐过敏高风险食物而无警告",
                "建议过量摄入某种营养素",
                "忽略年龄限制",
                "给出医疗诊断建议"
            ]
        }
    
    def auto_quality_check(self, conversation: Dict[str, Any]) -> Dict[str, Any]:
        """自动质量检查"""
        issues = []
        warnings = []
        
        instruction = conversation.get('instruction', '')
        output = conversation.get('output', '')
        metadata = conversation.get('metadata', {})
        
        # 检查红旗问题
        for red_flag in self.review_guidelines['red_flags']:
            if any(keyword in output.lower() for keyword in red_flag.lower().split()):
                issues.append(f"可能包含红旗内容: {red_flag}")
        
        # 检查年龄适宜性
        age_group = metadata.get('age_group', '')
        if age_group == '6-12m' and '固体' in output and '6个月前' in output:
            issues.append("可能建议6个月前婴儿食用固体食物")
        
        # 检查营养数据合理性
        if '千卡' in output:
            # 提取能量数值进行合理性检查
            import re
            energy_matches = re.findall(r'(\d+(?:\.\d+)?)\s*千卡', output)
            for energy_str in energy_matches:
                energy = float(energy_str)
                if energy > 1000:  # 单餐能量过高
                    warnings.append(f"单餐能量可能过高: {energy}千卡")
                elif energy < 10:  # 单餐能量过低
                    warnings.append(f"单餐能量可能过低: {energy}千卡")
        
        # 检查回答长度
        if len(output) < 100:
            warnings.append("回答可能过于简短")
        elif len(output) > 1000:
            warnings.append("回答可能过于冗长")
        
        # 检查专业术语使用
        professional_terms = ['营养', '蛋白质', '碳水化合物', '脂肪', '维生素', '矿物质']
        term_count = sum(1 for term in professional_terms if term in output)
        if term_count < 2:
            warnings.append("专业术语使用较少，可能不够专业")
        
        return {
            "issues": issues,
            "warnings": warnings,
            "auto_score": max(0, 5 - len(issues) - len(warnings) * 0.5),
            "needs_human_review": len(issues) > 0 or len(warnings) > 2
        }
    
    def get_conversations_for_review(self, limit: int = 10, 
                                   filter_criteria: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """获取需要审核的对话"""
        conversations = []
        
        for i, conv in enumerate(self.dataset[:limit]):
            conv_id = f"conv_{i:06d}"
            
            # 跳过已审核的对话
            if conv_id in self.reviews:
                continue
            
            # 应用过滤条件
            if filter_criteria:
                if not self._match_filter(conv, filter_criteria):
                    continue
            
            # 添加自动质量检查结果
            auto_check = self.auto_quality_check(conv)
            conv['auto_check'] = auto_check
            conv['conversation_id'] = conv_id
            
            conversations.append(conv)
        
        return conversations
    
    def _match_filter(self, conversation: Dict[str, Any], 
                     filter_criteria: Dict[str, Any]) -> bool:
        """检查对话是否匹配过滤条件"""
        metadata = conversation.get('metadata', {})
        
        if 'age_group' in filter_criteria:
            if metadata.get('age_group') != filter_criteria['age_group']:
                return False
        
        if 'type' in filter_criteria:
            if metadata.get('type') != filter_criteria['type']:
                return False
        
        if 'needs_review' in filter_criteria:
            auto_check = conversation.get('auto_check', {})
            if auto_check.get('needs_human_review', False) != filter_criteria['needs_review']:
                return False
        
        return True
    
    def submit_review(self, conversation_id: str, criteria: ReviewCriteria, 
                     improvements: List[str], action: str) -> bool:
        """提交审核结果"""
        try:
            # 计算总分
            scores = [criteria.accuracy, criteria.completeness, criteria.clarity, 
                     criteria.safety, criteria.age_appropriateness]
            criteria.overall_score = sum(scores) / len(scores)
            criteria.approved = criteria.overall_score >= 3.5 and action == "approve"
            criteria.review_date = datetime.now().isoformat()
            
            # 创建审核结果
            review_result = ReviewResult(
                conversation_id=conversation_id,
                criteria=criteria,
                suggested_improvements=improvements,
                action=action
            )
            
            # 保存审核结果
            self.reviews[conversation_id] = review_result
            self._save_reviews()
            
            logger.info(f"审核结果已保存: {conversation_id} - {action}")
            return True
            
        except Exception as e:
            logger.error(f"保存审核结果失败: {str(e)}")
            return False
    
    def _save_reviews(self):
        """保存审核结果"""
        review_path = self.dataset_path.replace('.json', '_reviews.json')
        reviews_data = [
            {
                "conversation_id": review.conversation_id,
                "criteria": asdict(review.criteria),
                "suggested_improvements": review.suggested_improvements,
                "action": review.action
            }
            for review in self.reviews.values()
        ]
        
        with open(review_path, 'w', encoding='utf-8') as f:
            json.dump(reviews_data, f, ensure_ascii=False, indent=2)
    
    def generate_quality_report(self) -> Dict[str, Any]:
        """生成质量报告"""
        if not self.reviews:
            return {"error": "暂无审核数据"}
        
        # 统计审核结果
        total_reviews = len(self.reviews)
        approved_count = sum(1 for r in self.reviews.values() if r.criteria.approved)
        
        # 计算各维度平均分
        avg_scores = {
            "accuracy": sum(r.criteria.accuracy for r in self.reviews.values()) / total_reviews,
            "completeness": sum(r.criteria.completeness for r in self.reviews.values()) / total_reviews,
            "clarity": sum(r.criteria.clarity for r in self.reviews.values()) / total_reviews,
            "safety": sum(r.criteria.safety for r in self.reviews.values()) / total_reviews,
            "age_appropriateness": sum(r.criteria.age_appropriateness for r in self.reviews.values()) / total_reviews,
            "overall": sum(r.criteria.overall_score for r in self.reviews.values()) / total_reviews
        }
        
        # 统计行动分布
        action_counts = {}
        for review in self.reviews.values():
            action_counts[review.action] = action_counts.get(review.action, 0) + 1
        
        # 收集改进建议
        all_improvements = []
        for review in self.reviews.values():
            all_improvements.extend(review.suggested_improvements)
        
        return {
            "summary": {
                "total_reviews": total_reviews,
                "approved_count": approved_count,
                "approval_rate": f"{approved_count/total_reviews*100:.1f}%"
            },
            "average_scores": avg_scores,
            "action_distribution": action_counts,
            "common_improvements": list(set(all_improvements)),
            "generated_at": datetime.now().isoformat()
        }
    
    def export_approved_dataset(self, output_path: str) -> int:
        """导出已审核通过的数据集"""
        approved_conversations = []
        
        for i, conv in enumerate(self.dataset):
            conv_id = f"conv_{i:06d}"
            review = self.reviews.get(conv_id)
            
            if review and review.criteria.approved:
                # 添加审核信息到元数据
                conv_copy = conv.copy()
                conv_copy['metadata']['reviewed'] = True
                conv_copy['metadata']['review_score'] = review.criteria.overall_score
                conv_copy['metadata']['reviewer_id'] = review.criteria.reviewer_id
                
                approved_conversations.append(conv_copy)
        
        # 保存审核通过的数据集
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(approved_conversations, f, ensure_ascii=False, indent=2)
        
        logger.info(f"已导出 {len(approved_conversations)} 条审核通过的对话到: {output_path}")
        return len(approved_conversations)

def create_review_interface():
    """创建Streamlit审核界面"""
    st.title("营养指导对话数据审核系统")
    
    # 侧边栏配置
    st.sidebar.header("配置")
    dataset_path = st.sidebar.text_input("数据集路径", "nutribench_llm_dataset.json")
    
    if not Path(dataset_path).exists():
        st.error(f"数据集文件不存在: {dataset_path}")
        return
    
    # 初始化质量控制器
    qc = QualityController(dataset_path)
    
    # 过滤选项
    st.sidebar.subheader("过滤条件")
    age_filter = st.sidebar.selectbox("年龄组", ["全部", "6-12m", "1-3y", "3-6y"])
    type_filter = st.sidebar.selectbox("对话类型", ["全部", "nutrition_analysis", "diversity_monitoring", "health_guidance"])
    
    filter_criteria = {}
    if age_filter != "全部":
        filter_criteria['age_group'] = age_filter
    if type_filter != "全部":
        filter_criteria['type'] = type_filter
    
    # 获取待审核对话
    conversations = qc.get_conversations_for_review(limit=50, filter_criteria=filter_criteria)
    
    if not conversations:
        st.info("没有找到需要审核的对话")
        return
    
    # 选择要审核的对话
    st.header("待审核对话")
    conv_options = [f"{conv['conversation_id']}: {conv['instruction'][:50]}..." for conv in conversations]
    selected_idx = st.selectbox("选择对话", range(len(conv_options)), format_func=lambda x: conv_options[x])
    
    if selected_idx is not None:
        conv = conversations[selected_idx]
        
        # 显示对话内容
        st.subheader("对话内容")
        st.text_area("用户问题", conv['instruction'], height=100, disabled=True)
        st.text_area("助手回答", conv['output'], height=200, disabled=True)
        
        # 显示元数据
        st.subheader("元数据")
        st.json(conv['metadata'])
        
        # 显示自动检查结果
        if 'auto_check' in conv:
            st.subheader("自动质量检查")
            auto_check = conv['auto_check']
            
            if auto_check['issues']:
                st.error("发现问题:")
                for issue in auto_check['issues']:
                    st.write(f"- {issue}")
            
            if auto_check['warnings']:
                st.warning("警告:")
                for warning in auto_check['warnings']:
                    st.write(f"- {warning}")
            
            st.write(f"自动评分: {auto_check['auto_score']:.1f}/5.0")
        
        # 人工审核表单
        st.subheader("人工审核")
        with st.form("review_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                accuracy = st.slider("准确性", 1, 5, 3)
                completeness = st.slider("完整性", 1, 5, 3)
                clarity = st.slider("清晰度", 1, 5, 3)
            
            with col2:
                safety = st.slider("安全性", 1, 5, 3)
                age_appropriateness = st.slider("年龄适宜性", 1, 5, 3)
            
            comments = st.text_area("审核意见")
            improvements = st.text_area("改进建议 (每行一条)")
            reviewer_id = st.text_input("审核员ID")
            
            action = st.radio("审核决定", ["approve", "reject", "revise"])
            
            submitted = st.form_submit_button("提交审核")
            
            if submitted:
                criteria = ReviewCriteria(
                    accuracy=accuracy,
                    completeness=completeness,
                    clarity=clarity,
                    safety=safety,
                    age_appropriateness=age_appropriateness,
                    comments=comments,
                    reviewer_id=reviewer_id
                )
                
                improvement_list = [imp.strip() for imp in improvements.split('\n') if imp.strip()]
                
                success = qc.submit_review(conv['conversation_id'], criteria, improvement_list, action)
                
                if success:
                    st.success("审核结果已保存!")
                    st.experimental_rerun()
                else:
                    st.error("保存审核结果失败")

def main():
    """主函数"""
    print("=== 数据质量控制系统 ===")
    
    # 示例：创建质量控制器
    try:
        qc = QualityController("nutribench_llm_dataset.json")
        
        # 获取需要审核的对话
        conversations = qc.get_conversations_for_review(limit=5)
        print(f"找到 {len(conversations)} 条需要审核的对话")
        
        # 生成质量报告
        report = qc.generate_quality_report()
        print("质量报告:", json.dumps(report, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"运行出错: {str(e)}")

if __name__ == "__main__":
    main()
