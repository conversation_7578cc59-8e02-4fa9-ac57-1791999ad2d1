#!/usr/bin/env python3
"""
演示集成分量转换功能的婴幼儿营养指导系统
展示如何将成人餐食按照官方标准转换为婴幼儿辅食分量
"""

import json
import pandas as pd
import random
from portion_conversion_system import InfantPortionConverter
from typing import Dict, List, Any

class PortionConversionDemo:
    """分量转换演示系统"""
    
    def __init__(self):
        self.portion_converter = InfantPortionConverter()
        
        # 模拟高质量的营养师回答模板
        self.response_templates = {
            "0-6m": {
                "nutrition_analysis": """作为专业营养师，我需要明确告诉您：

🚫 **重要提醒**
0-6个月的宝宝应该坚持纯母乳喂养，不需要添加任何辅食。

👶 **0-6个月正确喂养**
- 纯母乳喂养是最完美的营养来源
- 母乳含有宝宝所需的全部营养和抗体
- 按需哺乳，通常每天8-12次
- 每日补充维生素D 400IU

⚠️ **避免事项**
- 不添加任何辅食、果汁、水
- 不使用奶瓶和安抚奶嘴（可能影响母乳喂养）
- 过早添加辅食可能增加过敏和感染风险"""
            },
            
            "6m_start": {
                "nutrition_analysis": """基于分量转换分析，我来为6个月开始的宝宝提供专业指导：

📊 **分量转换分析**
- 成人餐食转换为婴幼儿辅食分量：{converted_energy}千卡
- 适合6个月宝宝的实际摄入量：{target_volume}ml（约2-3小勺）
- 转换比例：{conversion_ratio:.1%}

🥄 **6个月辅食添加标准**
- **分量控制**：从尝一两口开始，逐渐增加到2～3小勺
- **食物质地**：稠糊/肉泥/菜泥状，质地光滑无颗粒
- **添加频次**：从1次开始，逐渐推进到2次
- **营养来源**：母乳为主（提供80%营养），辅食为补充

✅ **制备建议**
- {preparation_tips}
- 用小勺喂食，让宝宝适应勺子
- 每次只添加一种新食物，观察3-5天

⚠️ **安全提醒**
- {safety_reminders}
- 保持食物新鲜，现做现吃""",
                
                "feeding_guidance": """为6个月开始的宝宝提供基于官方标准的喂养指导：

🎯 **官方辅食添加标准**
- **开始时间**：满6个月后开始添加辅食
- **标准分量**：从尝一两口开始，逐渐增加到2～3小勺（约15ml）
- **添加频次**：从1次开始，逐渐推进到2次
- **食物质地**：稠糊/肉泥/菜泥状

🍽️ **第一次辅食建议**
- 首选：强化铁的婴儿米粉
- 制备：调成稠糊状，不要太稀
- 分量：1-2小勺开始
- 时间：两次母乳之间

📅 **渐进式添加计划**
第1-3天：婴儿米粉（观察适应情况）
第4-7天：如适应良好，可增加分量
第8-10天：尝试添加菜泥（如胡萝卜泥）
第11-14天：如无过敏，可尝试果泥

🛡️ **重要原则**
- 继续母乳喂养（最重要！）
- 每种新食物观察3-5天
- 不强迫进食，尊重宝宝信号
- 营养来源：母乳为主，辅食为补充"""
            }
        }
    
    def generate_enhanced_response(self, meal_data: Dict, age_group: str, conversation_type: str) -> str:
        """生成集成分量转换的增强回答"""
        
        # 对于0-6个月，直接返回纯母乳喂养指导
        if age_group == "0-6m":
            template = self.response_templates.get("0-6m", {}).get(conversation_type, "")
            return template if template else "0-6个月宝宝应坚持纯母乳喂养。"
        
        # 对于6个月以上，进行分量转换
        try:
            conversion_result = self.portion_converter.convert_adult_portion_to_infant(meal_data, age_group)
            
            # 获取转换信息
            converted_nutrition = conversion_result['converted_nutrition']
            conversion_info = conversion_result['conversion_info']
            feeding_guidance = conversion_result['feeding_guidance']
            nutrition_analysis = conversion_result['nutrition_analysis']
            
            # 生成基于转换结果的专业回答
            if age_group == "6m_start" and conversation_type == "nutrition_analysis":
                template = self.response_templates["6m_start"]["nutrition_analysis"]
                return template.format(
                    converted_energy=converted_nutrition['energy_kcal'],
                    target_volume=conversion_info['target_infant_volume_ml'],
                    conversion_ratio=conversion_info['conversion_ratio'],
                    preparation_tips=feeding_guidance['preparation_tips'],
                    safety_reminders=feeding_guidance['safety_reminders']
                )
            
            elif age_group == "6m_start" and conversation_type == "feeding_guidance":
                return self.response_templates["6m_start"]["feeding_guidance"]
            
            else:
                # 通用的基于转换结果的回答
                return f"""基于分量转换的专业营养分析：

📊 **分量转换结果**
- 原始成人餐食：{meal_data.get('energy', 0)}千卡
- 转换为{conversion_result['age_standard']['name']}辅食分量：{converted_nutrition['energy_kcal']}千卡
- 官方标准分量：{conversion_result['age_standard']['portion_description']}
- 转换比例：{conversion_info['conversion_ratio']:.1%}

📈 **营养评估**
- {nutrition_analysis['overall_evaluation']}
- 能量水平：{nutrition_analysis['energy_assessment']}
- 蛋白质水平：{nutrition_analysis['protein_assessment']}

🥄 **制备和喂养指导**
- 食物质地：{feeding_guidance['preparation_tips']}
- 喂养频次：{feeding_guidance['frequency']}
- 营养来源：{feeding_guidance['main_nutrition_source']}

💡 **营养建议**
{nutrition_analysis['recommendations']}

⚠️ **安全提醒**
{feeding_guidance['safety_reminders']}"""
                
        except Exception as e:
            return f"分量转换分析失败：{str(e)}，建议咨询专业营养师。"
    
    def demonstrate_conversion_system(self):
        """演示完整的分量转换系统"""
        print("🍼 集成分量转换的婴幼儿营养指导系统演示")
        print("=" * 70)
        
        # 示例餐食数据
        sample_meals = [
            {
                "meal_description": "蒸蛋羹配胡萝卜泥和小米粥",
                "carb": 28.5,
                "protein": 12.8,
                "fat": 8.2,
                "energy": 235.0,
                "country": "CHN"
            },
            {
                "meal_description": "烤三文鱼配土豆泥和西兰花",
                "carb": 35.2,
                "protein": 28.6,
                "fat": 15.4,
                "energy": 378.0,
                "country": "USA"
            }
        ]
        
        age_groups = ["0-6m", "6m_start", "6-9m", "9-12m", "12-24m"]
        conversation_types = ["nutrition_analysis", "feeding_guidance"]
        
        for i, meal in enumerate(sample_meals, 1):
            print(f"\n📖 示例餐食 {i}: {meal['meal_description']}")
            print(f"原始营养: 能量{meal['energy']}kcal, 蛋白质{meal['protein']}g")
            print("=" * 70)
            
            for age_group in age_groups[:3]:  # 演示前3个年龄段
                for conv_type in conversation_types[:1]:  # 演示营养分析
                    print(f"\n【{age_group} - {conv_type}】")
                    
                    # 生成用户问题
                    if age_group == "0-6m":
                        question = f"我的宝宝0-6个月，我想给他/她吃{meal['meal_description']}，可以吗？"
                    else:
                        question = f"我的宝宝{age_group}，今天吃了{meal['meal_description']}，请分析营养价值。"
                    
                    print(f"问题: {question}")
                    
                    # 生成增强回答
                    response = self.generate_enhanced_response(meal, age_group, conv_type)
                    print(f"回答: {response[:300]}...")
                    
                    if len(response) > 300:
                        print("    [回答已截断，实际回答更详细]")
                    
                    print("-" * 50)
    
    def show_conversion_comparison(self):
        """展示转换前后的对比"""
        print("\n🔄 成人餐食 vs 婴幼儿辅食分量对比")
        print("=" * 70)
        
        sample_meal = {
            "meal_description": "鸡肉蔬菜粥",
            "carb": 45.0,
            "protein": 25.0,
            "fat": 8.0,
            "energy": 348.0,
            "country": "CHN"
        }
        
        print(f"📊 原始成人餐食: {sample_meal['meal_description']}")
        print(f"   营养成分: 能量{sample_meal['energy']}kcal, 蛋白质{sample_meal['protein']}g")
        print()
        
        for age_group in ["6m_start", "6-9m", "9-12m", "12-24m"]:
            try:
                conversion = self.portion_converter.convert_adult_portion_to_infant(sample_meal, age_group)
                
                print(f"🍽️ {conversion['age_standard']['name']}转换结果:")
                print(f"   官方标准: {conversion['age_standard']['portion_description']}")
                print(f"   转换比例: {conversion['conversion_info']['conversion_ratio']:.1%}")
                print(f"   转换后营养: 能量{conversion['converted_nutrition']['energy_kcal']}kcal, " +
                      f"蛋白质{conversion['converted_nutrition']['protein_g']}g")
                print(f"   营养评估: {conversion['nutrition_analysis']['overall_evaluation']}")
                print()
                
            except Exception as e:
                print(f"   转换失败: {e}")

def main():
    """主演示函数"""
    demo = PortionConversionDemo()
    
    # 演示完整系统
    demo.demonstrate_conversion_system()
    
    # 展示转换对比
    demo.show_conversion_comparison()
    
    print("\n🎉 核心优势总结:")
    print("✅ 基于官方辅食添加要点的科学分量转换")
    print("✅ 考虑母乳为主、辅食为辅的营养结构")
    print("✅ 年龄段精准匹配的营养密度调整")
    print("✅ 详细的制备和安全指导")
    print("✅ 真实营养数据支撑的个性化建议")

if __name__ == "__main__":
    main()
