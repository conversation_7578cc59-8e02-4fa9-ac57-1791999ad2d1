#!/usr/bin/env python3
"""
读取辅食添加要点.xlsx文件
"""

import pandas as pd
import sys
import subprocess
import json

def install_openpyxl():
    """安装openpyxl库"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'openpyxl'])
        print("✅ 成功安装openpyxl")
        return True
    except Exception as e:
        print(f"❌ 安装openpyxl失败: {e}")
        return False

def read_excel_file(filename):
    """读取Excel文件"""
    try:
        # 尝试读取Excel文件
        df = pd.read_excel(filename)
        return df
    except ImportError:
        print("📦 需要安装openpyxl库...")
        if install_openpyxl():
            try:
                df = pd.read_excel(filename)
                return df
            except Exception as e:
                print(f"❌ 安装后仍然失败: {e}")
                return None
        else:
            return None
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        return None

def main():
    """主函数"""
    print("📖 读取辅食添加要点.xlsx文件")
    print("=" * 50)
    
    filename = "辅食添加要点.xlsx"
    df = read_excel_file(filename)
    
    if df is None:
        print("❌ 无法读取Excel文件")
        return
    
    print(f"✅ 成功读取Excel文件")
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 列名: {list(df.columns)}")
    print()
    
    # 显示所有内容
    print("📖 辅食添加要点详细内容:")
    print("=" * 80)
    
    # 处理可能的空值和格式问题
    for i, row in df.iterrows():
        print(f"\n【第 {i+1} 行】")
        for col in df.columns:
            value = row[col]
            if pd.notna(value) and str(value).strip():
                print(f"  {col}: {value}")
    
    # 保存为JSON格式便于后续使用
    try:
        # 转换为字典格式
        data_dict = {}
        for i, row in df.iterrows():
            row_data = {}
            for col in df.columns:
                value = row[col]
                if pd.notna(value) and str(value).strip():
                    row_data[col] = str(value).strip()
            if row_data:  # 只保存非空行
                data_dict[f"row_{i+1}"] = row_data
        
        # 保存为JSON
        with open("辅食添加要点.json", "w", encoding="utf-8") as f:
            json.dump(data_dict, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 已保存为JSON格式: 辅食添加要点.json")
        
    except Exception as e:
        print(f"❌ 保存JSON失败: {e}")

if __name__ == "__main__":
    main()
