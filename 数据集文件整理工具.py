#!/usr/bin/env python3
"""
婴幼儿饮食数据集文件整理工具
自动分析和整理文件夹中的数据集文件
"""

import os
import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any
import shutil

class DatasetOrganizer:
    """数据集文件整理器"""
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        self.file_categories = {
            "核心数据集": [],
            "采样数据": [],
            "统计报告": [],
            "系统代码": [],
            "知识库": [],
            "文档指南": [],
            "原始数据": [],
            "其他文件": []
        }
        
    def analyze_files(self):
        """分析文件夹中的所有文件"""
        print("🔍 分析文件夹结构...")
        
        for file_path in self.base_path.iterdir():
            if file_path.is_file():
                self._categorize_file(file_path)
        
        self._print_analysis_results()
    
    def _categorize_file(self, file_path: Path):
        """对文件进行分类"""
        filename = file_path.name
        suffix = file_path.suffix.lower()
        
        # 核心数据集
        if any(keyword in filename for keyword in [
            "efficient_dataset", "enhanced_qwen", "qwen_0_3_years_demo", 
            "nutribench_llm_dataset"
        ]) and suffix == ".json":
            self.file_categories["核心数据集"].append({
                "file": filename,
                "size": self._get_file_size(file_path),
                "type": "对话数据集",
                "description": self._get_dataset_description(filename)
            })
        
        # 采样数据
        elif any(keyword in filename for keyword in [
            "smart_sample", "selected_samples"
        ]) and suffix in [".parquet", ".csv"]:
            self.file_categories["采样数据"].append({
                "file": filename,
                "size": self._get_file_size(file_path),
                "type": "采样数据",
                "description": self._get_sampling_description(filename)
            })
        
        # 统计报告
        elif any(keyword in filename for keyword in [
            "stats", "report", "sampling_report", "generation_stats"
        ]) and suffix == ".json":
            self.file_categories["统计报告"].append({
                "file": filename,
                "size": self._get_file_size(file_path),
                "type": "统计报告",
                "description": "数据生成或采样的统计分析"
            })
        
        # 系统代码
        elif suffix == ".py":
            self.file_categories["系统代码"].append({
                "file": filename,
                "size": self._get_file_size(file_path),
                "type": "Python脚本",
                "description": self._get_code_description(filename)
            })
        
        # 知识库
        elif any(keyword in filename for keyword in [
            "guidelines", "nutrition", "辅食添加", "infant_feeding"
        ]) and suffix in [".json", ".xlsx", ".doc"]:
            self.file_categories["知识库"].append({
                "file": filename,
                "size": self._get_file_size(file_path),
                "type": "知识库文件",
                "description": "官方指南和营养知识"
            })
        
        # 文档指南
        elif suffix == ".md":
            self.file_categories["文档指南"].append({
                "file": filename,
                "size": self._get_file_size(file_path),
                "type": "Markdown文档",
                "description": self._get_doc_description(filename)
            })
        
        # 原始数据
        elif "nutribench" in filename.lower() and suffix in [".parquet", ".csv"]:
            self.file_categories["原始数据"].append({
                "file": filename,
                "size": self._get_file_size(file_path),
                "type": "原始数据集",
                "description": "NutriBench原始数据"
            })
        
        # 其他文件
        else:
            self.file_categories["其他文件"].append({
                "file": filename,
                "size": self._get_file_size(file_path),
                "type": suffix[1:] if suffix else "未知",
                "description": "其他相关文件"
            })
    
    def _get_file_size(self, file_path: Path) -> str:
        """获取文件大小"""
        try:
            size_bytes = file_path.stat().st_size
            if size_bytes < 1024:
                return f"{size_bytes}B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes/1024:.1f}KB"
            else:
                return f"{size_bytes/(1024*1024):.1f}MB"
        except:
            return "未知"
    
    def _get_dataset_description(self, filename: str) -> str:
        """获取数据集描述"""
        if "efficient_dataset" in filename:
            return "高效生成的高质量对话数据集"
        elif "enhanced_qwen" in filename:
            return "基于Qwen模型的增强版数据集"
        elif "demo" in filename:
            return "演示用精选对话数据集"
        elif "nutribench_llm" in filename:
            return "基于NutriBench的基础训练集"
        else:
            return "婴幼儿营养对话数据集"
    
    def _get_sampling_description(self, filename: str) -> str:
        """获取采样数据描述"""
        if "balanced" in filename:
            return "均衡采样策略结果"
        elif "quality" in filename:
            return "质量优先采样结果"
        elif "stratified" in filename:
            return "分层采样策略结果"
        elif "cluster" in filename:
            return "聚类采样策略结果"
        elif "diversity" in filename:
            return "多样性采样策略结果"
        else:
            return "智能采样数据"
    
    def _get_code_description(self, filename: str) -> str:
        """获取代码文件描述"""
        descriptions = {
            "efficient_dataset_generator.py": "高效数据集生成器",
            "enhanced_qwen_builder.py": "增强版Qwen生成器",
            "simple_smart_sampling.py": "智能采样系统",
            "portion_conversion_system.py": "分量转换系统",
            "dataset_generator.py": "基础数据生成器",
            "demo_qwen_0_3_years.py": "Qwen系统演示",
            "demo_portion_conversion.py": "分量转换演示",
            "nutribench_usage_explanation.py": "数据使用说明",
            "数据集文件整理工具.py": "文件整理工具"
        }
        return descriptions.get(filename, "系统组件")
    
    def _get_doc_description(self, filename: str) -> str:
        """获取文档描述"""
        if "智能数据生成" in filename:
            return "完整解决方案技术文档"
        elif "分量转换" in filename:
            return "分量转换系统文档"
        elif "数据集性质" in filename:
            return "数据集性质分析报告"
        elif "完善总结" in filename:
            return "系统完善总结报告"
        elif "guide" in filename:
            return "使用指南文档"
        elif "整理报告" in filename:
            return "数据集整理报告"
        else:
            return "技术文档"
    
    def _print_analysis_results(self):
        """打印分析结果"""
        print("\n📊 文件分类分析结果")
        print("=" * 80)
        
        total_files = 0
        for category, files in self.file_categories.items():
            if files:
                print(f"\n📁 {category} ({len(files)}个文件)")
                print("-" * 50)
                for file_info in files:
                    print(f"  📄 {file_info['file']}")
                    print(f"      大小: {file_info['size']} | 类型: {file_info['type']}")
                    print(f"      描述: {file_info['description']}")
                    print()
                total_files += len(files)
        
        print(f"📊 总计: {total_files} 个文件")
    
    def create_organized_structure(self, output_dir: str = "organized_datasets"):
        """创建整理后的文件夹结构"""
        print(f"\n🗂️ 创建整理后的文件夹结构: {output_dir}")
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 创建分类文件夹
        for category in self.file_categories.keys():
            category_path = output_path / category
            category_path.mkdir(exist_ok=True)
        
        # 复制文件到对应文件夹
        for category, files in self.file_categories.items():
            if files:
                category_path = output_path / category
                print(f"📁 整理 {category}...")
                
                for file_info in files:
                    source_file = self.base_path / file_info['file']
                    target_file = category_path / file_info['file']
                    
                    try:
                        shutil.copy2(source_file, target_file)
                        print(f"  ✅ {file_info['file']}")
                    except Exception as e:
                        print(f"  ❌ {file_info['file']}: {e}")
        
        # 创建README文件
        self._create_readme(output_path)
        print(f"\n✅ 文件整理完成！整理后的文件保存在: {output_path}")
    
    def _create_readme(self, output_path: Path):
        """创建README文件"""
        readme_content = """# 婴幼儿饮食数据集文件整理

## 📁 文件夹结构说明

### 核心数据集
包含生成的高质量对话数据集，可直接用于模型训练。

### 采样数据  
智能采样策略的结果，包含精选的餐食数据。

### 统计报告
数据生成和采样过程的详细统计分析。

### 系统代码
完整的数据生成和处理系统代码。

### 知识库
官方喂养指南和营养知识库文件。

### 文档指南
技术文档和使用指南。

### 原始数据
NutriBench原始数据集。

### 其他文件
其他相关的支持文件。

## 🚀 快速开始

1. 查看 `文档指南/智能数据生成解决方案.md` 了解整体方案
2. 使用 `核心数据集/` 中的JSON文件进行模型训练
3. 参考 `系统代码/` 中的Python脚本进行定制化开发

## 📊 数据集统计

- 总对话数: 571条高质量对话
- 覆盖年龄段: 0-3岁完整覆盖
- 国家覆盖: 24个国家
- 对话类型: 6种专业类型

生成时间: {timestamp}
"""
        
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        readme_file = output_path / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content.format(timestamp=timestamp))
    
    def generate_summary_report(self):
        """生成汇总报告"""
        print("\n📋 生成数据集汇总报告...")
        
        # 分析核心数据集
        core_datasets = self.file_categories["核心数据集"]
        total_conversations = 0
        
        for dataset in core_datasets:
            if dataset['file'].endswith('.json'):
                try:
                    with open(self.base_path / dataset['file'], 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if isinstance(data, list):
                            total_conversations += len(data)
                            print(f"  📄 {dataset['file']}: {len(data)} 条对话")
                except Exception as e:
                    print(f"  ❌ 无法读取 {dataset['file']}: {e}")
        
        # 分析采样数据
        sampling_data = self.file_categories["采样数据"]
        total_samples = 0
        
        for sample in sampling_data:
            if sample['file'].endswith('.parquet'):
                try:
                    df = pd.read_parquet(self.base_path / sample['file'])
                    total_samples += len(df)
                    print(f"  📄 {sample['file']}: {len(df)} 条样本")
                except Exception as e:
                    print(f"  ❌ 无法读取 {sample['file']}: {e}")
        
        print(f"\n📊 汇总统计:")
        print(f"  总对话数: {total_conversations} 条")
        print(f"  总样本数: {total_samples} 条")
        print(f"  文件总数: {sum(len(files) for files in self.file_categories.values())} 个")

def main():
    """主函数"""
    print("🗂️ 婴幼儿饮食数据集文件整理工具")
    print("=" * 60)
    
    organizer = DatasetOrganizer()
    
    # 分析文件
    organizer.analyze_files()
    
    # 生成汇总报告
    organizer.generate_summary_report()
    
    # 询问是否创建整理后的文件夹结构
    create_organized = input("\n是否创建整理后的文件夹结构？(y/n): ").strip().lower()
    if create_organized in ['y', 'yes', '是']:
        organizer.create_organized_structure()
    
    print("\n🎉 文件整理完成！")

if __name__ == "__main__":
    main()
