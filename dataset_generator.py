#!/usr/bin/env python3
"""
NutriBench LLM训练数据集生成器
用于生成育幼健康监测-指导功能的LLM微调数据
"""

import json
import pandas as pd
import random
import os
from typing import Dict, List, Any
from jinja2 import Template

class NutriBenchDatasetGenerator:
    def __init__(self):
        # 专门针对0-3岁年龄段
        self.age_groups = {
            "0-6m": {
                "name": "0-6个月",
                "energy_need": 550,
                "main_food": "纯母乳/配方奶",
                "stage": "纯母乳喂养期",
                "key_nutrition": ["母乳", "维生素D"]
            },
            "6-12m": {
                "name": "6-12个月",
                "energy_need": 750,
                "main_food": "母乳+辅食",
                "stage": "辅食添加期",
                "key_nutrition": ["母乳", "铁", "锌", "辅食"]
            },
            "12-24m": {
                "name": "12-24个月",
                "energy_need": 1000,
                "main_food": "多样化食物",
                "stage": "幼儿期过渡",
                "key_nutrition": ["钙", "优质蛋白", "多样化食物"]
            },
            "24-36m": {
                "name": "24-36个月",
                "energy_need": 1200,
                "main_food": "均衡膳食",
                "stage": "幼儿期",
                "key_nutrition": ["全面营养", "膳食纤维", "钙"]
            }
        }
        
        self.nutrition_templates = self._load_templates()
        self.guidelines = self._load_guidelines()
    
    def _load_templates(self) -> Dict[str, Template]:
        """加载对话模板"""
        templates = {
            "nutrition_analysis": Template("""
用户: 我的宝宝{{ age }}了，今天吃了{{ meal_description }}，请帮我分析一下营养成分。

助手: 根据您描述的餐食，我来为您详细分析营养成分：

📊 **营养成分分析**
- 碳水化合物：{{ carb }}克
- 蛋白质：{{ protein }}克  
- 脂肪：{{ fat }}克
- 总能量：{{ energy }}千卡

📈 **年龄适宜性评估**
对于{{ age }}的宝宝来说：
{{ age_assessment }}

💡 **营养建议**
{{ nutrition_advice }}

⚠️ **注意事项**
{{ precautions }}
            """.strip()),
            
            "diversity_monitoring": Template("""
用户: 我想了解这个餐食"{{ meal_description }}"对{{ age }}宝宝的饮食多样性有什么帮助？

助手: 让我为您分析这个餐食的营养价值和多样性贡献：

🍽️ **餐食营养分析**
- 能量：{{ energy }}千卡
- 蛋白质：{{ protein }}克
- 脂肪：{{ fat }}克  
- 碳水化合物：{{ carb }}克

🌈 **饮食多样性评估**
{{ diversity_analysis }}

📋 **膳食平衡建议**
{{ balance_advice }}

🎯 **下一餐建议**
{{ next_meal_suggestion }}
            """.strip()),
            
            "health_guidance": Template("""
用户: 宝宝{{ age }}，体重{{ weight }}kg，今天吃了{{ meal_description }}，营养够吗？还需要补充什么？

助手: 根据您提供的信息，我来为您做专业的营养评估：

👶 **宝宝基本情况**
- 年龄：{{ age }}
- 体重：{{ weight }}kg
- 每日能量需求：约{{ daily_energy_need }}千卡

🍎 **今日餐食分析**
{{ meal_analysis }}

📊 **营养缺口分析**
{{ nutrition_gap }}

🥗 **补充建议**
{{ supplement_advice }}

📅 **膳食规划建议**
{{ meal_planning }}
            """.strip())
        }
        return templates
    
    def _load_guidelines(self) -> Dict[str, Any]:
        """加载营养指南数据"""
        return {
            "dri_2013": {
                "0-6m": {"energy": 550, "protein": 9, "fat": 31, "carb": 60},
                "6-12m": {"energy": 750, "protein": 15, "fat": 30, "carb": 95},
                "1-3y": {"energy": 1200, "protein": 25, "fat": 35, "carb": 150},
                "3-6y": {"energy": 1600, "protein": 35, "fat": 45, "carb": 200}
            },
            "feeding_principles": {
                "0-6m": "纯母乳喂养，按需哺乳",
                "6-12m": "继续母乳喂养，逐步添加辅食",
                "1-3y": "食物多样化，培养良好饮食习惯",
                "3-6y": "均衡膳食，适量运动"
            }
        }
    
    def generate_nutrition_analysis(self, meal_data: Dict, age_group: str) -> Dict[str, Any]:
        """生成营养分析对话"""
        age_info = self.age_groups[age_group]
        
        # 生成年龄评估
        energy_ratio = meal_data['energy'] / age_info['energy_need']
        if energy_ratio > 0.4:
            age_assessment = f"这餐的能量较高，占{age_group}宝宝日需求的{energy_ratio:.1%}，建议适量食用。"
        elif energy_ratio > 0.2:
            age_assessment = f"这餐的能量适中，占{age_group}宝宝日需求的{energy_ratio:.1%}，营养密度良好。"
        else:
            age_assessment = f"这餐的能量较低，占{age_group}宝宝日需求的{energy_ratio:.1%}，可作为加餐。"
        
        # 生成营养建议
        nutrition_advice = self._generate_nutrition_advice(meal_data, age_group)
        
        # 生成注意事项
        precautions = self._generate_precautions(meal_data, age_group)
        
        template_data = {
            **meal_data,
            'age': age_info['name'],
            'age_assessment': age_assessment,
            'nutrition_advice': nutrition_advice,
            'precautions': precautions
        }
        
        conversation = self.nutrition_templates['nutrition_analysis'].render(**template_data)
        
        return {
            "instruction": conversation.split('\n助手:')[0].replace('用户: ', '').strip(),
            "output": conversation.split('\n助手: ')[1].strip(),
            "metadata": {
                "type": "nutrition_analysis",
                "age_group": age_group,
                "country": meal_data.get('country', 'Unknown'),
                "energy_level": "high" if energy_ratio > 0.4 else "medium" if energy_ratio > 0.2 else "low"
            }
        }
    
    def _generate_nutrition_advice(self, meal_data: Dict, age_group: str) -> str:
        """生成针对0-3岁的营养建议"""
        advice = []
        age_info = self.age_groups[age_group]

        # 0-6个月特殊处理
        if age_group == "0-6m":
            advice.append("0-6个月宝宝应坚持纯母乳喂养，不需要添加任何辅食。")
            advice.append("如果无法母乳喂养，应选择适合的婴儿配方奶。")
            advice.append("每日补充维生素D 400IU，促进钙吸收和骨骼发育。")
            return " ".join(advice)

        # 6个月以上的营养建议
        if age_group == "6-12m":
            advice.append("继续母乳喂养的同时，及时添加营养丰富的辅食。")
            if meal_data['protein'] > 5:
                advice.append("蛋白质含量适宜，有助于快速生长发育。")
            if meal_data.get('energy', 0) > 100:
                advice.append("能量密度良好，适合辅食添加期的营养需求。")
            advice.append("注意食物质地从泥状逐渐过渡到碎末状。")

        elif age_group in ["12-24m", "24-36m"]:
            if meal_data['protein'] > 8:
                advice.append("蛋白质含量丰富，支持肌肉和器官发育。")
            if meal_data['carb'] > 15:
                advice.append("碳水化合物充足，为活跃的幼儿提供充足能量。")
            if age_group == "12-24m":
                advice.append("继续母乳喂养至2岁，同时确保食物多样化。")
            else:
                advice.append("培养良好饮食习惯，鼓励自主进食。")

        # 通用脂肪建议（对大脑发育重要）
        if age_group in ['6-12m', '12-24m'] and meal_data['fat'] > 3:
            advice.append("脂肪含量适宜，为大脑发育提供必需脂肪酸。")

        return " ".join(advice) if advice else f"营养搭配适合{age_info['name']}宝宝，建议配合其他食物形成均衡膳食。"
    
    def _generate_precautions(self, meal_data: Dict, age_group: str) -> str:
        """生成针对0-3岁的安全注意事项"""
        precautions = []

        if age_group == '0-6m':
            precautions.append("0-6个月宝宝应坚持纯母乳喂养，不添加任何辅食、水或其他液体。")
            precautions.append("如需使用配方奶，请选择适合年龄的产品并正确冲调。")
            precautions.append("避免使用奶瓶和安抚奶嘴，以免影响母乳喂养。")

        elif age_group == '6-12m':
            precautions.append("辅食添加应从6个月开始，每次只添加一种新食物，观察3-5天。")
            precautions.append("食物质地应适合月龄：6-7月龄泥状，8-9月龄碎末状，10-12月龄小颗粒。")
            precautions.append("避免蜂蜜、坚果、整颗葡萄等有窒息风险的食物。")
            precautions.append("注意观察过敏反应：皮疹、腹泻、呕吐等症状。")

        elif age_group in ['12-24m', '24-36m']:
            precautions.append("确保食物充分煮熟，避免生冷食物。")
            precautions.append("切小圆形食物（如葡萄、樱桃番茄）以防窒息。")
            precautions.append("建立规律进餐时间，避免边吃边玩。")
            if age_group == '24-36m':
                precautions.append("鼓励自主进食，但需要成人监督确保安全。")

        # 通用安全提醒
        if meal_data.get('energy', 0) > 400:
            precautions.append("单餐能量较高，注意控制总量，避免过度喂养。")

        precautions.append("保持食物新鲜卫生，餐具清洁消毒。")

        return " ".join(precautions)

    def _analyze_food_diversity(self, meal_data: Dict) -> str:
        """分析食物多样性"""
        description = meal_data['meal_description'].lower()

        food_groups = {
            '谷物': ['米', '面', '粥', '粉', '麦', '燕麦'],
            '蔬菜': ['菜', '萝卜', '白菜', '菠菜', '胡萝卜', '西红柿'],
            '水果': ['苹果', '香蕉', '橙', '梨', '葡萄', '草莓'],
            '蛋白质': ['肉', '鱼', '蛋', '豆', '奶', '虾'],
            '脂肪': ['油', '坚果', '芝麻', '核桃']
        }

        identified_groups = []
        for group, keywords in food_groups.items():
            if any(keyword in description for keyword in keywords):
                identified_groups.append(group)

        if len(identified_groups) >= 3:
            return f"这餐包含了{len(identified_groups)}个食物类别（{', '.join(identified_groups)}），食物多样性较好。"
        elif len(identified_groups) == 2:
            return f"这餐包含了{len(identified_groups)}个食物类别（{', '.join(identified_groups)}），建议增加其他类别食物。"
        else:
            return "这餐食物种类相对单一，建议增加不同类别的食物以提高营养多样性。"

    def _generate_balance_advice(self, meal_data: Dict, age_group: str) -> str:
        """生成膳食平衡建议"""
        energy_ratio = meal_data['energy'] / self.age_groups[age_group]['energy_need']

        advice = []
        if energy_ratio < 0.15:
            advice.append("这餐能量较低，适合作为加餐或点心。")
        elif energy_ratio > 0.4:
            advice.append("这餐能量较高，建议控制份量或减少其他餐次的摄入。")

        # 营养素比例分析
        total_macros = meal_data['carb'] * 4 + meal_data['protein'] * 4 + meal_data['fat'] * 9
        if total_macros > 0:
            carb_percent = (meal_data['carb'] * 4) / total_macros * 100
            protein_percent = (meal_data['protein'] * 4) / total_macros * 100
            fat_percent = (meal_data['fat'] * 9) / total_macros * 100

            if protein_percent < 10:
                advice.append("蛋白质比例偏低，建议增加优质蛋白质食物。")
            if fat_percent < 20 and age_group in ['6-12m', '1-3y']:
                advice.append("脂肪比例偏低，婴幼儿需要适量脂肪促进大脑发育。")

        return " ".join(advice) if advice else "营养比例基本合理，继续保持均衡饮食。"

    def _generate_next_meal_suggestion(self, meal_data: Dict, age_group: str) -> str:
        """生成下一餐建议"""
        suggestions = []

        if meal_data['protein'] < 5:
            suggestions.append("下一餐可以增加蛋类、肉类或豆制品")
        if meal_data['carb'] < 15:
            suggestions.append("可以添加米饭、面条等主食")
        if '蔬菜' not in meal_data['meal_description']:
            suggestions.append("建议加入新鲜蔬菜")
        if '水果' not in meal_data['meal_description']:
            suggestions.append("可以搭配时令水果")

        if suggestions:
            return f"建议下一餐：{', '.join(suggestions)}，以补充本餐不足的营养。"
        else:
            return "本餐营养较为全面，下一餐可以尝试不同的食物搭配，保持饮食多样性。"

    def _generate_typical_weight(self, age_group: str) -> float:
        """生成典型体重"""
        weight_ranges = {
            '6-12m': (7.0, 10.0),
            '1-3y': (10.0, 15.0),
            '3-6y': (15.0, 22.0)
        }
        min_w, max_w = weight_ranges[age_group]
        return round(random.uniform(min_w, max_w), 1)

    def _analyze_meal_nutrition(self, meal_data: Dict, age_group: str) -> str:
        """分析餐食营养"""
        energy_ratio = meal_data['energy'] / self.age_groups[age_group]['energy_need']

        analysis = f"本餐提供能量{meal_data['energy']:.1f}千卡，约占全天需求的{energy_ratio:.1%}。"

        if meal_data['protein'] > 8:
            analysis += " 蛋白质含量丰富，有助于生长发育。"
        elif meal_data['protein'] < 3:
            analysis += " 蛋白质含量偏低，需要在其他餐次补充。"

        return analysis

    def _analyze_nutrition_gap(self, meal_data: Dict, age_group: str) -> str:
        """分析营养缺口"""
        dri = self.guidelines['dri_2013'][age_group]

        gaps = []
        if meal_data['protein'] < dri['protein'] * 0.3:
            gaps.append("蛋白质")
        if meal_data['energy'] < dri['energy'] * 0.25:
            gaps.append("总能量")

        if gaps:
            return f"相对于全天需求，本餐在{', '.join(gaps)}方面还有提升空间。"
        else:
            return "本餐营养密度良好，基本满足该餐次的营养需求。"

    def _generate_supplement_advice(self, meal_data: Dict, age_group: str) -> str:
        """生成补充建议"""
        advice = []

        if meal_data['protein'] < 5:
            advice.append("• 增加蛋白质：可添加蒸蛋、肉泥、鱼泥或豆腐")
        if meal_data['carb'] < 20:
            advice.append("• 补充主食：适量米粥、面条或红薯")
        if meal_data['fat'] < 3 and age_group in ['6-12m', '1-3y']:
            advice.append("• 适量脂肪：可加入少量植物油或坚果泥")

        # 微量营养素建议
        if age_group == '6-12m':
            advice.append("• 继续补充维生素D 400IU/天")
            advice.append("• 注意铁质补充，选择强化铁米粉")

        return "\n".join(advice) if advice else "营养搭配合理，继续保持多样化饮食即可。"

    def _generate_meal_planning(self, age_group: str) -> str:
        """生成膳食规划建议"""
        planning = {
            '6-12m': "建议每日5-6餐：母乳/配方奶4-5次，辅食1-2次。逐步增加辅食种类和质地。",
            '1-3y': "建议每日5餐：三正餐+两次加餐。保证奶类500ml/天，食物种类多样化。",
            '3-6y': "建议每日3正餐+1-2次加餐。培养良好饮食习惯，鼓励自主进食。"
        }
        return planning.get(age_group, "根据年龄特点安排合理的餐次和食物种类。")
    
    def generate_diversity_monitoring(self, meal_data: Dict, age_group: str) -> Dict[str, Any]:
        """生成饮食多样性监测对话"""
        age_info = self.age_groups[age_group]

        # 分析食物多样性
        diversity_analysis = self._analyze_food_diversity(meal_data)
        balance_advice = self._generate_balance_advice(meal_data, age_group)
        next_meal_suggestion = self._generate_next_meal_suggestion(meal_data, age_group)

        template_data = {
            **meal_data,
            'age': age_info['name'],
            'diversity_analysis': diversity_analysis,
            'balance_advice': balance_advice,
            'next_meal_suggestion': next_meal_suggestion
        }

        conversation = self.nutrition_templates['diversity_monitoring'].render(**template_data)

        return {
            "instruction": conversation.split('\n助手:')[0].replace('用户: ', '').strip(),
            "output": conversation.split('\n助手: ')[1].strip(),
            "metadata": {
                "type": "diversity_monitoring",
                "age_group": age_group,
                "country": meal_data.get('country', 'Unknown')
            }
        }

    def generate_health_guidance(self, meal_data: Dict, age_group: str) -> Dict[str, Any]:
        """生成健康指导对话"""
        age_info = self.age_groups[age_group]

        # 生成体重和营养需求
        weight = self._generate_typical_weight(age_group)
        daily_energy_need = age_info['energy_need']

        meal_analysis = self._analyze_meal_nutrition(meal_data, age_group)
        nutrition_gap = self._analyze_nutrition_gap(meal_data, age_group)
        supplement_advice = self._generate_supplement_advice(meal_data, age_group)
        meal_planning = self._generate_meal_planning(age_group)

        template_data = {
            **meal_data,
            'age': age_info['name'],
            'weight': weight,
            'daily_energy_need': daily_energy_need,
            'meal_analysis': meal_analysis,
            'nutrition_gap': nutrition_gap,
            'supplement_advice': supplement_advice,
            'meal_planning': meal_planning
        }

        conversation = self.nutrition_templates['health_guidance'].render(**template_data)

        return {
            "instruction": conversation.split('\n助手:')[0].replace('用户: ', '').strip(),
            "output": conversation.split('\n助手: ')[1].strip(),
            "metadata": {
                "type": "health_guidance",
                "age_group": age_group,
                "country": meal_data.get('country', 'Unknown')
            }
        }

    def generate_dataset(self, nutribench_data: pd.DataFrame, sample_size: int = 1000) -> List[Dict[str, Any]]:
        """生成完整的训练数据集"""
        dataset = []

        # 随机采样
        sampled_data = nutribench_data.sample(n=min(sample_size, len(nutribench_data)))

        for _, row in sampled_data.iterrows():
            meal_data = row.to_dict()

            # 为每个餐食生成多个年龄段的对话（专注0-3岁）
            for age_group in ['0-6m', '6-12m', '12-24m', '24-36m']:  # 0-3岁完整覆盖
                try:
                    # 生成营养分析对话
                    nutrition_qa = self.generate_nutrition_analysis(meal_data, age_group)
                    dataset.append(nutrition_qa)

                    # 生成饮食多样性监测对话
                    diversity_qa = self.generate_diversity_monitoring(meal_data, age_group)
                    dataset.append(diversity_qa)

                    # 生成健康指导对话
                    guidance_qa = self.generate_health_guidance(meal_data, age_group)
                    dataset.append(guidance_qa)

                except Exception as e:
                    print(f"生成对话时出错: {e}")
                    continue

        return dataset

def load_nutribench_data(data_path: str = "NutriBench") -> pd.DataFrame:
    """加载NutriBench数据集"""
    print("🔄 加载NutriBench数据集...")

    try:
        # 尝试加载v2版本（推荐）
        v2_path = f"{data_path}/v2/train-00000-of-00001.parquet"
        if os.path.exists(v2_path):
            df = pd.read_parquet(v2_path)
            print(f"✅ 成功加载v2数据: {len(df)} 条记录")
            print(f"📊 覆盖国家: {df['country'].nunique()} 个")
            print(f"🌍 国家列表: {sorted(df['country'].unique())}")
            return df
    except Exception as e:
        print(f"❌ v2数据加载失败: {e}")

    try:
        # 尝试加载v1版本
        print("🔄 尝试加载v1数据...")
        v1_files = [
            f"{data_path}/v1/wweia_meal_natural-00000-of-00001.parquet",
            f"{data_path}/v1/wweia_meal_metric-00000-of-00001.parquet",
            f"{data_path}/v1/who_meal_natural-00000-of-00001.parquet",
            f"{data_path}/v1/who_meal_metric-00000-of-00001.parquet"
        ]

        all_data = []
        for file_path in v1_files:
            if os.path.exists(file_path):
                try:
                    df_part = pd.read_parquet(file_path)
                    df_part['source'] = os.path.basename(file_path).replace('-00000-of-00001.parquet', '')
                    all_data.append(df_part)
                    print(f"✅ 加载 {os.path.basename(file_path)}: {len(df_part)} 条")
                except Exception as e:
                    print(f"❌ 跳过 {file_path}: {e}")

        if all_data:
            df = pd.concat(all_data, ignore_index=True)
            print(f"✅ 成功合并v1数据: {len(df)} 条记录")
            return df
    except Exception as e:
        print(f"❌ v1数据加载失败: {e}")

    # 如果都失败了，使用示例数据
    print("⚠️ 使用示例数据...")
    return pd.DataFrame([
        {
            "meal_description": "一小碗白米粥配蒸蛋羹",
            "carb": 25.5,
            "protein": 8.2,
            "fat": 4.1,
            "energy": 165.3,
            "country": "CHN"
        },
        {
            "meal_description": "苹果泥配婴儿米粉",
            "carb": 18.7,
            "protein": 2.1,
            "fat": 0.8,
            "energy": 89.2,
            "country": "CHN"
        }
    ])

def main():
    """主函数示例"""
    # 加载真实的NutriBench数据
    nutribench_data = load_nutribench_data()

    # 创建数据生成器
    generator = NutriBenchDatasetGenerator()

    # 生成数据集（使用更多样本）
    sample_size = min(50, len(nutribench_data))  # 使用50个样本或全部数据
    dataset = generator.generate_dataset(nutribench_data, sample_size=sample_size)

    # 保存数据集
    output_file = 'nutribench_llm_dataset_enhanced.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(dataset, f, ensure_ascii=False, indent=2)

    print(f"\n🎉 生成完成!")
    print(f"📊 生成了 {len(dataset)} 条训练数据")
    print(f"💾 保存到: {output_file}")

    # 显示统计信息
    types = {}
    ages = {}
    for item in dataset:
        t = item['metadata']['type']
        a = item['metadata']['age_group']
        types[t] = types.get(t, 0) + 1
        ages[a] = ages.get(a, 0) + 1

    print(f"\n📈 数据分布:")
    print(f"对话类型: {types}")
    print(f"年龄组: {ages}")

    print(f"\n📖 示例数据:")
    print(json.dumps(dataset[0], ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
