#!/usr/bin/env python3
"""
Qwen 0-3岁婴幼儿营养数据集构建演示
模拟Qwen API调用，展示专门针对0-3岁年龄段的数据生成
"""

import json
import pandas as pd
import random
import os
from typing import Dict, List, Any

class MockQwenAPI:
    """模拟Qwen API，基于官方辅食添加要点生成高质量回答"""

    def __init__(self):
        # 加载官方辅食添加要点
        self.feeding_points = self._load_feeding_points()
        self.response_templates = {
            "0-6m": {
                "nutrition_analysis": """作为专业的婴幼儿营养师，我需要明确告诉您：

🚫 **重要提醒**
0-6个月的宝宝应该坚持纯母乳喂养，不需要添加任何辅食，包括您提到的{meal_description}。

👶 **0-6个月宝宝的营养需求**
- 母乳是最完美的营养来源，含有宝宝所需的全部营养
- 母乳中的抗体能保护宝宝免受感染
- 按需哺乳，通常每天8-12次

💡 **专业建议**
- 如果无法母乳喂养，选择适合0-6个月的婴儿配方奶
- 每日补充维生素D 400IU
- 定期监测宝宝的生长发育

⚠️ **安全提醒**
过早添加辅食可能增加过敏、感染和消化不良的风险。请耐心等到6个月后再开始添加辅食。""",

                "feeding_guidance": """针对0-6个月宝宝的喂养，我给您以下专业指导：

🤱 **纯母乳喂养原则**
- 生后1小时内开始母乳喂养
- 按需哺乳，不限制次数和时间
- 夜间也要坚持母乳喂养

🍼 **配方奶喂养（当无法母乳时）**
- 选择适合0-6个月的一段奶粉
- 严格按照说明书配制，不要过浓或过稀
- 奶瓶和奶嘴要彻底消毒

💊 **营养补充**
- 维生素D：每日400IU，从出生后数日开始
- 不需要额外补充钙、铁等其他营养素

📊 **生长监测**
- 定期测量体重、身长、头围
- 关注宝宝的精神状态和大小便情况

❌ **避免事项**
- 不添加任何辅食、果汁、水
- 不使用安抚奶嘴（可能影响母乳喂养）"""
            },
            
            "6m_start": {
                "nutrition_analysis": """作为专业的婴幼儿营养师，我来为刚满6个月的宝宝进行辅食添加分析：

🎯 **6个月辅食添加关键原则**
根据官方《辅食添加要点》，6个月开始添加辅食需要遵循以下标准：

📊 **官方指导标准**
- **喂养频次**：继续母乳喂养，从1次辅食开始，逐渐推进到2次
- **每餐分量**：从尝一两口开始，逐渐增加到2～3小勺
- **食物质地**：稠糊/肉泥/菜泥状
- **营养要求**：每天辅食种类不少于4种

🍽️ **7大类辅食分类标准**
1. 谷薯/主食类（糊糊、软饭、面条、土豆等）
2. 动物性食物（鱼、禽、肉及内脏）
3. 蛋类
4. 奶类和奶制品（以奶粉、酸奶、奶为主要原料的食物）
5. 豆类和坚果制品（豆米、豆腐、芝麻酱、花生酱等）
6. 富含维生素A的蔬菜和水果（南瓜、红心红薯、芒果等）
7. 其它蔬菜和水果（白菜、西蓝花、苹果、梨等）

✅ **基本要求**
至少包括一种动物性食物、一种蔬菜和一种谷薯类食物

⚠️ **安全第一**
- 每次只添加一种新食物，观察3-5天
- 食物无盐无糖，不添加调料
- 注意过敏反应：皮疹、腹泻、呕吐""",

                "feeding_guidance": """为刚满6个月的宝宝提供官方标准的辅食添加指导：

📋 **官方辅食添加时间表**

**6个月开始阶段**：
- 继续母乳喂养（最重要！）
- 从1次辅食开始，逐渐推进到2次
- 从尝一两口开始，逐渐增加到2～3小勺
- 食物质地：稠糊/肉泥/菜泥

🥄 **第一次辅食建议**
- 首选：强化铁的婴儿米粉
- 质地：调成稠糊状，不要太稀
- 分量：1-2小勺开始
- 时间：两次母乳之间

📅 **渐进式添加计划**
第1-3天：婴儿米粉（观察适应情况）
第4-7天：如适应良好，可增加分量
第8-10天：尝试添加菜泥（如胡萝卜泥）
第11-14天：如无过敏，可尝试果泥

🛡️ **安全监测要点**
- 每种新食物观察3-5天
- 注意大便变化、皮肤反应
- 保持食物新鲜，现做现吃
- 用小勺喂食，培养进食技能""",
            },

            "6-9m": {
                "nutrition_analysis": """根据官方《辅食添加要点》，我来为6～9月龄宝宝进行专业分析：

📊 **6～9月龄官方标准**
- **喂养频次**：继续母乳喂养，逐渐推进（半）固体食物摄入到1～2次
- **每餐分量**：每餐2～3勺，逐渐增加到1/2碗（250ml的碗）
- **食物质地**：稠糊/糊糊/粥烂/蒸烂的家庭食物

🍽️ **营养成分评估**
这个餐食含有碳水化合物{carb}克、蛋白质{protein}克、脂肪{fat}克，总能量{energy}千卡。

📈 **适宜性分析**
- 能量密度：{energy_assessment}，适合6-9月龄快速生长需求
- 蛋白质含量：{protein_assessment}，支持肌肉和器官发育
- 质地要求：需要调整为稠糊状或蒸烂的家庭食物

🎯 **7大类食物覆盖检查**
确保每天包含：
✅ 谷薯/主食类 ✅ 动物性食物 ✅ 蛋类 ✅ 奶类制品
✅ 豆类坚果制品 ✅ 维A蔬果 ✅ 其它蔬果

⚠️ **重要提醒**
- 每天辅食种类不少于4种
- 至少包括一种动物性食物、一种蔬菜和一种谷薯类食物
- 继续母乳喂养，辅食是补充而非替代""",

                "feeding_guidance": """为6-12个月宝宝提供专业的辅食添加指导：

🍽️ **辅食添加时间表**
- 6个月：婴儿米粉、菜泥、果泥
- 7-8个月：蛋黄、肉泥、鱼泥
- 9-11个月：碎菜、碎肉、面条
- 12个月：接近成人食物质地

🥄 **喂养技巧**
- 用小勺喂食，让宝宝适应勺子
- 从1-2勺开始，逐渐增加量
- 尊重宝宝的饥饱信号

📅 **进餐安排**
- 母乳：每日4-6次
- 辅食：每日2-3次
- 建立规律的进餐时间

🛡️ **安全第一**
- 每次只添加一种新食物
- 观察过敏反应：皮疹、腹泻、呕吐
- 食物充分煮熟，确保卫生"""
            },
            
            "12-24m": {
                "nutrition_analysis": """为12-24个月幼儿进行营养分析：

📊 **营养评估**
餐食能量{energy}千卡，蛋白质{protein}克，适合快速生长期的营养需求。

🌟 **12-24个月特点**
- 生长速度仍然很快，需要营养密度高的食物
- 开始学会自主进食，培养良好饮食习惯
- 消化系统逐渐成熟，可以接受更多样的食物

🥗 **膳食建议**
- 继续母乳喂养至2岁
- 每日三餐两点，规律进食
- 食物种类多样化，包含各大营养素

💪 **重点营养**
- 钙：每日需要600mg，促进骨骼发育
- 优质蛋白：支持快速生长
- 铁：预防缺铁性贫血

🍽️ **进食技能培养**
- 鼓励用勺子、叉子进食
- 让宝宝参与简单的食物准备
- 培养独立进食的能力""",

                "problem_solving": """针对12-24个月宝宝的喂养问题，我提供以下解决方案：

🤔 **常见问题及对策**

**挑食偏食：**
- 多次尝试，不强迫进食
- 提供多样化食物选择
- 家长做好示范作用

**拒绝新食物：**
- 耐心引导，可能需要尝试10-15次
- 让宝宝参与食物准备过程
- 创造愉快的进餐氛围

**进食技能发展：**
- 提供适合的餐具
- 允许宝宝用手抓食物
- 逐步教会使用勺子、叉子

**营养均衡：**
- 确保每餐都有蛋白质、碳水化合物、蔬菜
- 控制零食，不影响正餐
- 保证充足的奶类摄入

💡 **专业提醒**
这个阶段的宝宝正在建立终生的饮食习惯，家长的耐心和正确引导非常重要。"""
            },
            
            "24-36m": {
                "nutrition_analysis": """为24-36个月幼儿进行全面营养分析：

📊 **营养状况评估**
这个餐食提供能量{energy}千卡，营养构成适合2-3岁幼儿的发育需求。

🎯 **24-36个月营养重点**
- 全面均衡的营养摄入
- 培养良好的饮食习惯
- 支持大脑和身体的快速发育

🍎 **每日营养建议**
- 谷类：85-100克
- 蔬菜：150-200克  
- 水果：150-200克
- 肉蛋类：75-100克
- 奶类：400-500毫升

🧠 **发育支持**
- DHA：促进大脑发育
- 钙：强化骨骼和牙齿
- 维生素：支持免疫系统

👨‍👩‍👧‍👦 **家庭饮食**
- 鼓励与家人一起进餐
- 学习使用餐具的正确方法
- 培养感恩和分享的品质""",

                "feeding_guidance": """24-36个月幼儿喂养的专业指导：

🍽️ **膳食安排**
- 三餐两点的规律作息
- 每餐包含多种食物类别
- 控制零食，不影响正餐食欲

👶 **自主进食培养**
- 鼓励独立使用餐具
- 让孩子参与简单的烹饪活动
- 培养对食物的兴趣和认知

🥛 **饮品选择**
- 以白开水为主要饮品
- 限制果汁和含糖饮料
- 保证充足的奶类摄入

🌈 **食物多样性**
- 每周尝试1-2种新食物
- 不同颜色的蔬菜水果搭配
- 多种烹调方式增加食物吸引力

📚 **营养教育**
- 教孩子认识不同食物
- 讲解营养与健康的关系
- 培养不浪费食物的好习惯"""
            }
        }

    def _load_feeding_points(self) -> Dict[str, Any]:
        """加载官方辅食添加要点"""
        try:
            with open('辅食添加要点.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print("⚠️ 未找到辅食添加要点.json文件")
            return {}

    def generate_response(self, age_group: str, conversation_type: str, meal_data: Dict) -> str:
        """生成模拟回答"""
        templates = self.response_templates.get(age_group, {})
        template = templates.get(conversation_type, "")
        
        if not template:
            return f"针对{age_group}宝宝的{conversation_type}，建议咨询专业的儿科医生或营养师。"
        
        # 生成评估内容
        energy = meal_data.get('energy', 0)
        protein = meal_data.get('protein', 0)
        
        energy_assessment = "适中" if 50 <= energy <= 300 else "偏高" if energy > 300 else "偏低"
        protein_assessment = "丰富" if protein > 8 else "适中" if protein > 3 else "偏低"
        
        try:
            return template.format(
                meal_description=meal_data.get('meal_description', '该食物'),
                carb=meal_data.get('carb', 0),
                protein=protein,
                fat=meal_data.get('fat', 0),
                energy=energy,
                energy_assessment=energy_assessment,
                protein_assessment=protein_assessment
            )
        except KeyError:
            return template

class QwenDemo0_3Years:
    """Qwen 0-3岁演示类"""
    
    def __init__(self):
        self.mock_api = MockQwenAPI()
        self.age_groups = {
            "0-6m": {"name": "0-6个月", "stage": "纯母乳喂养期"},
            "6m_start": {"name": "6个月开始", "stage": "辅食添加初期"},
            "6-9m": {"name": "6～9月龄", "stage": "辅食适应期"},
            "9-12m": {"name": "9～12月龄", "stage": "辅食进阶期"},
            "12-24m": {"name": "12～24月龄", "stage": "幼儿期过渡"}
        }
        
        self.conversation_types = [
            "nutrition_analysis", "feeding_guidance", 
            "safety_advice", "problem_solving"
        ]
    
    def load_nutribench_data(self) -> pd.DataFrame:
        """加载NutriBench数据"""
        try:
            df = pd.read_parquet('NutriBench/v2/train-00000-of-00001.parquet')
            return df.sample(n=10, random_state=42)  # 演示用小样本
        except:
            # 使用示例数据
            return pd.DataFrame([
                {
                    "meal_description": "For breakfast, I ate a plain bun weighing 126 grams and sprinkled on 27 grams of raw sugar.",
                    "carb": 90.8, "protein": 9.6, "fat": 4.2, "energy": 439.0, "country": "ZMB"
                },
                {
                    "meal_description": "I've got 9 grams of boiled kasepa fish, 34 grams of raw maize flour, 3 grams of onion.",
                    "carb": 51.2, "protein": 8.7, "fat": 14.2, "energy": 363.0, "country": "ZMB"
                }
            ])
    
    async def generate_conversations(self, sample_size: int = 8) -> List[Dict[str, Any]]:
        """生成演示对话"""
        print("🚀 开始生成0-3岁专用营养指导对话...")
        
        nutribench_data = self.load_nutribench_data()
        conversations = []
        
        for i in range(sample_size):
            # 随机选择餐食、年龄组和对话类型
            meal_data = nutribench_data.iloc[i % len(nutribench_data)].to_dict()
            age_group = random.choice(list(self.age_groups.keys()))
            conv_type = random.choice(self.conversation_types)
            
            # 生成用户问题
            user_question = self._generate_user_question(meal_data, age_group, conv_type)
            
            # 生成助手回答
            assistant_response = self.mock_api.generate_response(age_group, conv_type, meal_data)
            
            conversation = {
                "instruction": user_question,
                "output": assistant_response,
                "metadata": {
                    "type": conv_type,
                    "age_group": age_group,
                    "stage": self.age_groups[age_group]["stage"],
                    "country": meal_data.get("country", "Unknown"),
                    "generation_method": "qwen_demo",
                    "meal_energy": meal_data.get("energy", 0)
                }
            }
            
            conversations.append(conversation)
            print(f"✅ 生成对话 {i+1}/{sample_size}: {conv_type} ({age_group})")
        
        return conversations
    
    def _generate_user_question(self, meal_data: Dict, age_group: str, conv_type: str) -> str:
        """生成用户问题"""
        age_name = self.age_groups[age_group]["name"]
        meal_desc = meal_data.get('meal_description', '这个食物')
        
        questions = {
            "0-6m": {
                "nutrition_analysis": f"我的宝宝{age_name}，我想给他/她吃{meal_desc}，这样合适吗？",
                "feeding_guidance": f"我的{age_name}宝宝，关于喂养方面需要什么指导？",
                "safety_advice": f"对于{age_name}的宝宝，有什么安全注意事项？"
            },
            "6-12m": {
                "nutrition_analysis": f"我的宝宝{age_name}，今天吃了{meal_desc}，请分析一下营养价值。",
                "feeding_guidance": f"我的{age_name}宝宝刚开始添加辅食，应该怎么安排？",
                "safety_advice": f"给{age_name}宝宝准备辅食时，有什么安全要求？"
            },
            "12-24m": {
                "nutrition_analysis": f"我的宝宝{age_name}，今天的餐食是{meal_desc}，营养够吗？",
                "problem_solving": f"我的{age_name}宝宝最近挑食，该怎么办？",
                "feeding_guidance": f"如何为{age_name}的宝宝安排合理的饮食？"
            },
            "24-36m": {
                "nutrition_analysis": f"我的宝宝{age_name}，请评估一下{meal_desc}的营养价值。",
                "feeding_guidance": f"如何培养{age_name}宝宝良好的饮食习惯？",
                "problem_solving": f"我的{age_name}宝宝不爱吃蔬菜，有什么好办法？"
            }
        }
        
        age_questions = questions.get(age_group, {})
        return age_questions.get(conv_type, f"关于{age_name}宝宝的营养，请给我一些建议。")

async def main():
    """主演示函数"""
    print("=" * 60)
    print("🤖 Qwen 0-3岁婴幼儿营养数据集构建演示")
    print("=" * 60)
    
    demo = QwenDemo0_3Years()
    
    # 生成演示对话
    conversations = await demo.generate_conversations(sample_size=12)
    
    # 保存数据集
    output_file = "qwen_0_3_years_demo_dataset.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(conversations, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 演示数据集已保存到: {output_file}")
    
    # 统计分析
    type_counts = {}
    age_counts = {}
    for conv in conversations:
        metadata = conv['metadata']
        type_counts[metadata['type']] = type_counts.get(metadata['type'], 0) + 1
        age_counts[metadata['age_group']] = age_counts.get(metadata['age_group'], 0) + 1
    
    print(f"\n📊 生成统计:")
    print(f"  总对话数: {len(conversations)}")
    print(f"  对话类型分布: {type_counts}")
    print(f"  年龄组分布: {age_counts}")
    
    # 展示样本
    print(f"\n📖 样本对话展示:")
    for i, conv in enumerate(conversations[:2]):
        print(f"\n--- 样本 {i+1} ---")
        print(f"年龄组: {conv['metadata']['age_group']} ({conv['metadata']['stage']})")
        print(f"类型: {conv['metadata']['type']}")
        print(f"问题: {conv['instruction']}")
        print(f"回答: {conv['output'][:300]}...")
    
    print(f"\n🎉 演示完成！生成了 {len(conversations)} 条专业的0-3岁营养指导对话")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
