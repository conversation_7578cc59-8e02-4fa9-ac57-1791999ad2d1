# NutriBench LLM微调数据集生成方案

## 项目目标
基于NutriBench数据集生成用于微调LLM的文本数据集，支持育幼健康监测-指导功能，专注于饮食多样性监测。

## 数据集分析

### NutriBench数据集特点
- **规模**: v2版本15,617样本，覆盖24个国家
- **字段**: meal_description, carb, protein, fat, energy, country, serving_type
- **来源**: WWEIA(美国) + FAO/WHO Gift(国际)
- **特色**: 真实世界数据，多国饮食文化

## 文本数据集生成策略

### 1. 对话模板设计

#### A. 营养评估类对话
```
用户: 我的宝宝今天吃了[meal_description]，请帮我分析一下营养成分。
助手: 根据您描述的餐食，我来为您分析营养成分：
- 碳水化合物：[carb]克
- 蛋白质：[protein]克  
- 脂肪：[fat]克
- 总能量：[energy]千卡

[基于年龄段的营养建议]
```

#### B. 饮食多样性监测类对话
```
用户: 我想了解这个餐食[meal_description]对[年龄]个月宝宝的营养价值如何？
助手: 这个餐食的营养分析如下：
[营养成分分析]
[与该年龄段推荐摄入量对比]
[饮食多样性评估]
[改进建议]
```

#### C. 健康指导类对话
```
用户: 宝宝[年龄]个月了，今天吃了[meal_description]，还需要补充什么营养？
助手: [基于国家卫健委指南的专业建议]
```

### 2. 数据增强策略

#### A. 年龄段适配
- 0-6个月：母乳/配方奶为主
- 6-12个月：辅食添加期
- 1-3岁：幼儿期营养需求
- 3-6岁：学龄前儿童营养

#### B. 场景多样化
- 日常餐食评估
- 营养不良预警
- 过敏食物识别
- 膳食平衡建议
- 生长发育监测

#### C. 文化适配
- 中国传统饮食文化
- 地方特色食物
- 节庆饮食习俗
- 现代生活方式

### 3. 知识库整合

#### A. 权威指南
- 《托育机构婴幼儿喂养与营养指南》
- 《中国居民膳食指南(2022)》
- 《中国7-24月龄婴幼儿喂养指南》
- WHO婴幼儿喂养建议

#### B. 营养标准
- 中国营养学会DRIs(2013)
- 各年龄段能量需要量
- 宏量营养素推荐比例
- 微量营养素需求

### 4. 数据质量控制

#### A. 专业性验证
- 营养学专家审核
- 医学准确性检查
- 年龄适宜性评估

#### B. 多样性保证
- 食物种类覆盖度
- 烹饪方式多样性
- 文化背景平衡

#### C. 安全性检查
- 过敏原标识
- 不适宜食物警告
- 年龄禁忌提醒

## 实施计划

### 阶段1: 数据准备(1-2周)
- 下载完整NutriBench数据集
- 构建营养指南知识库
- 设计对话模板框架

### 阶段2: 数据生成(2-3周)  
- 批量生成基础问答对
- 年龄段适配处理
- 场景多样化扩展

### 阶段3: 质量优化(1-2周)
- 专业性验证
- 数据去重和清洗
- 格式标准化

### 阶段4: 测试验证(1周)
- 小规模微调测试
- 效果评估和调优
- 最终数据集发布

## 预期产出

### 数据集规模
- 基础问答对：50,000+条
- 多轮对话：10,000+组
- 专业场景：5,000+条

### 数据格式
```json
{
  "instruction": "用户指令/问题",
  "input": "输入上下文(可选)",
  "output": "期望的助手回答",
  "metadata": {
    "age_group": "适用年龄段",
    "nutrition_focus": "营养重点",
    "source": "数据来源"
  }
}
```

## 技术实现

### 工具和框架
- Python + Pandas (数据处理)
- Jinja2 (模板引擎)
- OpenAI API (内容生成辅助)
- 专业营养数据库

### 质量保证
- 自动化检查脚本
- 人工审核流程
- A/B测试验证

## 预期效果

生成的LLM将具备：
1. 准确的营养成分分析能力
2. 专业的育幼健康指导
3. 个性化的饮食建议
4. 多样性监测和预警
5. 符合中国国情的本土化建议
