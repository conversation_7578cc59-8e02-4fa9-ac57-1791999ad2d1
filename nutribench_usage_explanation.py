#!/usr/bin/env python3
"""
详细解释NutriBench数据集的使用方式
展示如何将成人餐食数据转换为婴幼儿营养指导对话
"""

import pandas as pd
import json
import random

def analyze_nutribench_structure():
    """分析NutriBench数据集结构"""
    print("🔍 NutriBench数据集结构分析")
    print("=" * 60)
    
    try:
        # 加载数据集
        df = pd.read_parquet('NutriBench/v2/train-00000-of-00001.parquet')
        print(f"✅ 成功加载数据集: {len(df)} 条记录")
        print(f"📋 数据列: {list(df.columns)}")
        print()
        
        # 显示详细的数据样本
        print("📖 NutriBench数据样本详解:")
        print("-" * 60)
        
        for i in range(3):
            print(f"\n【样本 {i+1}】")
            row = df.iloc[i]
            print(f"  餐食描述: {row['meal_description']}")
            print(f"  碳水化合物: {row['carb']:.2f}g")
            print(f"  蛋白质: {row['protein']:.2f}g") 
            print(f"  脂肪: {row['fat']:.2f}g")
            print(f"  总能量: {row['energy']:.2f}kcal")
            print(f"  来源国家: {row['country']}")
        
        # 数据统计
        print(f"\n📊 数据集统计:")
        print(f"  覆盖国家: {df['country'].nunique()} 个")
        print(f"  国家列表: {sorted(df['country'].unique())}")
        print(f"  能量范围: {df['energy'].min():.1f} - {df['energy'].max():.1f} kcal")
        print(f"  蛋白质范围: {df['protein'].min():.1f} - {df['protein'].max():.1f} g")
        print(f"  碳水化合物范围: {df['carb'].min():.1f} - {df['carb'].max():.1f} g")
        print(f"  脂肪范围: {df['fat'].min():.1f} - {df['fat'].max():.1f} g")
        
        return df
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def demonstrate_data_transformation(df):
    """演示数据转换过程"""
    print("\n🔄 数据转换过程演示")
    print("=" * 60)
    
    # 选择一个样本进行详细演示
    sample = df.iloc[0].to_dict()
    
    print("🍽️ 原始NutriBench数据:")
    print(f"  餐食描述: {sample['meal_description']}")
    print(f"  营养成分: 碳水{sample['carb']:.1f}g, 蛋白质{sample['protein']:.1f}g, 脂肪{sample['fat']:.1f}g")
    print(f"  总能量: {sample['energy']:.1f}kcal")
    print(f"  来源国家: {sample['country']}")
    
    print("\n⬇️ 转换为婴幼儿营养指导对话:")
    print("-" * 40)
    
    # 演示不同年龄段的转换
    age_groups = {
        "0-6m": "0-6个月",
        "6-12m": "6-12个月", 
        "12-24m": "12-24个月"
    }
    
    for age_key, age_name in age_groups.items():
        print(f"\n【{age_name}宝宝】")
        
        # 生成用户问题
        user_question = f"我的宝宝{age_name}，今天吃了{sample['meal_description']}，请帮我分析一下营养成分。"
        
        # 生成营养师回答
        if age_key == "0-6m":
            response = f"""作为专业营养师，我需要明确告诉您：

🚫 **重要提醒**
{age_name}的宝宝应该坚持纯母乳喂养，不需要添加任何辅食，包括您提到的餐食。

👶 **正确的喂养方式**
- 纯母乳喂养是最佳选择
- 如无法母乳，选择适合的配方奶
- 每日补充维生素D 400IU
- 定期监测生长发育"""
        
        else:
            # 计算年龄适宜性
            daily_need = 750 if age_key == "6-12m" else 1000
            energy_percent = (sample['energy'] / daily_need) * 100
            
            response = f"""根据您描述的餐食，我来为{age_name}宝宝进行专业分析：

📊 **营养成分分析**
- 碳水化合物：{sample['carb']:.1f}克
- 蛋白质：{sample['protein']:.1f}克
- 脂肪：{sample['fat']:.1f}克
- 总能量：{sample['energy']:.1f}千卡

📈 **年龄适宜性评估**
这餐能量占{age_name}宝宝日需求的{energy_percent:.1f}%

💡 **营养建议**
{'蛋白质含量丰富，有助于生长发育。' if sample['protein'] > 10 else '建议搭配蛋白质丰富的食物。'}
{'脂肪含量适宜，支持大脑发育。' if sample['fat'] > 5 else '可适当增加健康脂肪。'}

⚠️ **注意事项**
{'辅食添加应循序渐进，注意观察过敏反应。' if age_key == '6-12m' else '确保食物质地适合宝宝咀嚼能力。'}"""
        
        print(f"  问题: {user_question}")
        print(f"  回答: {response}")

def show_conversation_generation_process():
    """展示对话生成的完整流程"""
    print("\n🤖 对话生成完整流程")
    print("=" * 60)
    
    # 加载数据
    df = pd.read_parquet('NutriBench/v2/train-00000-of-00001.parquet')
    
    print("步骤1️⃣: 从NutriBench随机选择餐食数据")
    sample_meal = df.sample(1).iloc[0].to_dict()
    print(f"  选中餐食: {sample_meal['meal_description'][:50]}...")
    print(f"  营养数据: 能量{sample_meal['energy']:.0f}kcal, 蛋白质{sample_meal['protein']:.1f}g")
    
    print("\n步骤2️⃣: 随机选择目标年龄组")
    age_group = random.choice(["0-6m", "6-12m", "12-24m", "24-36m"])
    print(f"  选中年龄组: {age_group}")
    
    print("\n步骤3️⃣: 随机选择对话类型")
    conversation_type = random.choice(["nutrition_analysis", "feeding_guidance", "safety_advice"])
    print(f"  选中对话类型: {conversation_type}")
    
    print("\n步骤4️⃣: 生成用户问题")
    user_prompts = {
        "0-6m": {
            "nutrition_analysis": f"我的宝宝0-6个月，我想给他/她吃{sample_meal['meal_description']}，这样合适吗？"
        },
        "6-12m": {
            "nutrition_analysis": f"我的宝宝6-12个月，今天吃了{sample_meal['meal_description']}，请分析营养价值。",
            "feeding_guidance": f"我的6-12个月宝宝，关于{sample_meal['meal_description']}这类食物应该怎么准备？"
        },
        "12-24m": {
            "nutrition_analysis": f"我的宝宝12-24个月，今天的餐食是{sample_meal['meal_description']}，营养够吗？",
            "safety_advice": f"给12-24个月宝宝准备{sample_meal['meal_description']}时，有什么安全要求？"
        }
    }
    
    user_question = user_prompts.get(age_group, {}).get(conversation_type, "请给我营养建议")
    print(f"  生成问题: {user_question}")
    
    print("\n步骤5️⃣: 基于官方指南生成专业回答")
    print("  回答包含:")
    print("    - 年龄段特异性指导")
    print("    - 基于实际营养数据的分析")
    print("    - 官方辅食添加要点")
    print("    - 安全注意事项")
    
    print("\n步骤6️⃣: 构建最终对话数据")
    conversation = {
        "instruction": user_question,
        "output": "[基于官方指南的专业回答]",
        "metadata": {
            "type": conversation_type,
            "age_group": age_group,
            "country": sample_meal["country"],
            "meal_energy": sample_meal["energy"],
            "generation_method": "nutribench_enhanced"
        }
    }
    
    print("  最终对话结构:")
    for key, value in conversation.items():
        if key == "output":
            print(f"    {key}: [专业营养师回答，包含具体营养分析和指导]")
        else:
            print(f"    {key}: {value}")

def explain_key_advantages():
    """解释使用NutriBench的关键优势"""
    print("\n🎯 使用NutriBench数据集的关键优势")
    print("=" * 60)
    
    advantages = [
        {
            "title": "🌍 真实多样的餐食数据",
            "description": "包含24个国家的15,617条真实餐食记录，涵盖不同文化背景的饮食习惯"
        },
        {
            "title": "📊 精确的营养成分数据", 
            "description": "每条记录都有准确的碳水化合物、蛋白质、脂肪、能量数据，为营养分析提供科学基础"
        },
        {
            "title": "🔄 灵活的数据转换",
            "description": "可以将成人餐食数据转换为适合不同年龄段婴幼儿的营养指导"
        },
        {
            "title": "📈 大规模数据生成",
            "description": "15,617条原始数据 × 多个年龄段 × 多种对话类型 = 数十万条潜在训练数据"
        },
        {
            "title": "🎯 个性化指导",
            "description": "基于具体的营养数据生成针对性建议，而非通用模板回答"
        },
        {
            "title": "🛡️ 安全性保障",
            "description": "结合年龄段特点，自动生成相应的安全提醒和注意事项"
        }
    ]
    
    for i, advantage in enumerate(advantages, 1):
        print(f"\n{i}. {advantage['title']}")
        print(f"   {advantage['description']}")

def show_practical_examples():
    """展示实际应用例子"""
    print("\n💡 实际应用例子")
    print("=" * 60)
    
    # 模拟几个实际的转换例子
    examples = [
        {
            "original": "For breakfast, I ate 2 slices of whole wheat toast with peanut butter and banana.",
            "carb": 45.2, "protein": 12.8, "fat": 18.5, "energy": 385.0,
            "age_group": "12-24m",
            "transformed_question": "我的宝宝12-24个月，今天早餐吃了全麦面包配花生酱和香蕉，营养怎么样？",
            "key_points": [
                "分析能量密度是否适合幼儿",
                "评估蛋白质含量对生长发育的作用", 
                "提醒花生过敏风险",
                "建议食物质地调整"
            ]
        },
        {
            "original": "I had a bowl of rice with steamed vegetables and grilled chicken.",
            "carb": 52.3, "protein": 28.6, "fat": 8.2, "energy": 378.0,
            "age_group": "9-12m",
            "transformed_question": "我的宝宝9-12个月，今天吃了米饭配蒸蔬菜和鸡肉，请分析营养价值。",
            "key_points": [
                "符合7大类食物分类要求",
                "蛋白质含量丰富，适合快速生长期",
                "建议调整为细切碎质地",
                "提醒逐步引入新蔬菜"
            ]
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n例子{i}: {example['age_group']}宝宝")
        print(f"  原始餐食: {example['original']}")
        print(f"  营养数据: 能量{example['energy']}kcal, 蛋白质{example['protein']}g")
        print(f"  转换问题: {example['transformed_question']}")
        print(f"  分析要点:")
        for point in example['key_points']:
            print(f"    • {point}")

def main():
    """主函数"""
    print("🔍 NutriBench数据集使用方式详解")
    print("=" * 80)
    
    # 1. 分析数据集结构
    df = analyze_nutribench_structure()
    
    if df is not None:
        # 2. 演示数据转换
        demonstrate_data_transformation(df)
        
        # 3. 展示对话生成流程
        show_conversation_generation_process()
        
        # 4. 解释关键优势
        explain_key_advantages()
        
        # 5. 实际应用例子
        show_practical_examples()
        
        print("\n" + "=" * 80)
        print("🎉 总结：NutriBench为我们提供了丰富的真实餐食数据，")
        print("   通过智能转换，可以生成大量高质量的婴幼儿营养指导对话！")
    
    else:
        print("❌ 无法加载数据集，请检查文件路径")

if __name__ == "__main__":
    main()
