#!/usr/bin/env python3
"""
并行生成测试脚本
测试并行处理与串行处理的性能差异
"""

import time
import pandas as pd
from efficient_dataset_generator import EfficientDatasetGenerator

def test_performance_comparison():
    """性能对比测试"""
    print("🧪 并行处理性能测试")
    print("=" * 60)
    
    api_key = "sk-5eba46fbcff649d5bf28313bc865de10"
    
    # 准备测试数据
    test_data = [
        {
            "meal_description": "Grilled chicken breast with steamed broccoli",
            "energy": 250.0,
            "protein": 30.0,
            "country": "USA"
        },
        {
            "meal_description": "Rice porridge with minced pork",
            "energy": 180.0,
            "protein": 12.0,
            "country": "CHN"
        },
        {
            "meal_description": "Banana and oatmeal",
            "energy": 120.0,
            "protein": 4.0,
            "country": "USA"
        }
    ]
    
    conversations_per_meal = 2  # 每餐生成2个对话
    total_expected = len(test_data) * conversations_per_meal
    
    print(f"📊 测试数据: {len(test_data)} 餐食")
    print(f"📊 每餐对话: {conversations_per_meal} 个")
    print(f"📊 预期总数: {total_expected} 条对话")
    print()
    
    # 测试1: 串行处理
    print("🐌 测试1: 串行处理")
    print("-" * 40)
    
    generator_serial = EfficientDatasetGenerator(
        api_key=api_key, 
        enable_parallel=False
    )
    
    start_time = time.time()
    conversations_serial = generator_serial.generate_conversations_batch(
        test_data, 
        conversations_per_meal=conversations_per_meal,
        use_parallel=False
    )
    serial_time = time.time() - start_time
    
    print(f"⏱️ 串行处理时间: {serial_time:.2f} 秒")
    print(f"📊 生成对话数: {len(conversations_serial)} 条")
    print()
    
    # 测试2: 并行处理 (3线程)
    print("⚡ 测试2: 并行处理 (3线程)")
    print("-" * 40)
    
    generator_parallel = EfficientDatasetGenerator(
        api_key=api_key, 
        max_workers=3,
        enable_parallel=True
    )
    
    start_time = time.time()
    conversations_parallel = generator_parallel.generate_conversations_batch(
        test_data, 
        conversations_per_meal=conversations_per_meal,
        use_parallel=True,
        max_workers=3
    )
    parallel_time = time.time() - start_time
    
    print(f"⏱️ 并行处理时间: {parallel_time:.2f} 秒")
    print(f"📊 生成对话数: {len(conversations_parallel)} 条")
    print()
    
    # 性能对比
    print("📈 性能对比结果")
    print("=" * 40)
    
    if serial_time > 0 and parallel_time > 0:
        speedup = serial_time / parallel_time
        time_saved = serial_time - parallel_time
        efficiency = (len(conversations_parallel) / parallel_time) / (len(conversations_serial) / serial_time) if serial_time > 0 else 0
        
        print(f"⚡ 加速比: {speedup:.2f}x")
        print(f"⏰ 节省时间: {time_saved:.2f} 秒")
        print(f"📊 效率提升: {efficiency:.2f}x")
        print(f"🎯 推荐配置: {'并行处理' if speedup > 1.2 else '串行处理'}")
    
    print()
    print("💡 优化建议:")
    print("- 对于大批量数据(>50条)，建议使用并行处理")
    print("- 线程数建议设置为3-5，避免API限制")
    print("- 如遇到频繁API错误，可降低并行度或使用串行模式")

def quick_test():
    """快速测试"""
    print("🚀 快速并行测试")
    print("=" * 40)
    
    api_key = "sk-5eba46fbcff649d5bf28313bc865de10"
    
    # 创建并行生成器
    generator = EfficientDatasetGenerator(
        api_key=api_key,
        max_workers=3,
        enable_parallel=True
    )
    
    # 使用balanced策略快速测试
    print("📊 使用balanced策略进行快速测试...")
    
    # 修改策略为小规模测试
    generator.generation_strategies["test"] = {
        "description": "测试策略 - 小规模快速验证",
        "sample_size": 5,  # 只采样5个餐食
        "conversations_per_meal": 2,  # 每餐2个对话
        "expected_output": 10  # 预期10条对话
    }
    
    start_time = time.time()
    result = generator.generate_efficient_dataset("test")
    total_time = time.time() - start_time
    
    if result:
        print(f"✅ 测试成功!")
        print(f"⏱️ 总耗时: {total_time:.2f} 秒")
        print(f"📊 生成对话: {len(result['conversations'])} 条")
        print(f"📁 输出文件: {result['files']['dataset']}")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        quick_test()
    else:
        test_performance_comparison()
