#!/usr/bin/env python3
"""
高效数据集生成器
结合智能采样策略和分量转换系统，高效生成高质量的婴幼儿营养数据集
"""

import pandas as pd
import json
import time
import random
import asyncio
import aiohttp
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Dict, List, Any, Optional
from smart_data_generation_strategy import SmartDataGenerationStrategy
from portion_conversion_system import InfantPortionConverter
from enhanced_qwen_builder import QwenConfig, EnhancedQwenBuilder

class ParallelQwenBuilder:
    """并行Qwen API调用器"""

    def __init__(self, config: QwenConfig, max_workers: int = 5):
        self.config = config
        self.max_workers = max_workers
        self.base_builder = EnhancedQwenBuilder(config)

    def generate_conversation_worker(self, args):
        """单个对话生成工作函数"""
        meal_data, age_group, conv_type = args
        try:
            conversation = self.base_builder.generate_enhanced_conversation(
                meal_data, age_group, conv_type
            )
            if conversation:
                return {"success": True, "data": conversation, "error": None}
            else:
                return {"success": False, "data": None, "error": "API返回空结果"}
        except Exception as e:
            return {"success": False, "data": None, "error": str(e)}

    def batch_generate_parallel(self, tasks: List[tuple], delay_between_batches: float = 0.5) -> List[Dict[str, Any]]:
        """并行批量生成对话"""
        print(f"🚀 启动并行生成，工作线程数: {self.max_workers}")
        print(f"📊 总任务数: {len(tasks)}")

        all_conversations = []
        successful_count = 0
        failed_count = 0

        # 分批处理，避免API限制
        batch_size = self.max_workers * 2  # 每批处理的任务数

        for i in range(0, len(tasks), batch_size):
            batch_tasks = tasks[i:i + batch_size]
            print(f"\n📦 处理批次 {i//batch_size + 1}/{(len(tasks) + batch_size - 1)//batch_size}")
            print(f"   任务范围: {i+1}-{min(i+batch_size, len(tasks))}")

            # 使用线程池并行处理当前批次
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_task = {
                    executor.submit(self.generate_conversation_worker, task): task
                    for task in batch_tasks
                }

                # 收集结果
                for future in as_completed(future_to_task):
                    result = future.result()
                    if result["success"]:
                        all_conversations.append(result["data"])
                        successful_count += 1
                        print(f"  ✅ 成功 ({successful_count}/{len(tasks)})")
                    else:
                        failed_count += 1
                        print(f"  ❌ 失败: {result['error']} ({failed_count}/{len(tasks)})")

            # 批次间延迟
            if i + batch_size < len(tasks):
                print(f"   ⏱️ 批次间延迟 {delay_between_batches}s...")
                time.sleep(delay_between_batches)

        success_rate = successful_count / len(tasks) * 100 if len(tasks) > 0 else 0
        print(f"\n🎉 并行生成完成!")
        print(f"📊 成功: {successful_count}, 失败: {failed_count}, 成功率: {success_rate:.1f}%")

        return all_conversations

class EfficientDatasetGenerator:
    """高效数据集生成器"""
    
    def __init__(self, api_key: str, max_workers: int = 5, enable_parallel: bool = True):
        self.api_key = api_key
        self.max_workers = max_workers
        self.enable_parallel = enable_parallel
        self.portion_converter = InfantPortionConverter()

        # 配置Qwen
        self.qwen_config = QwenConfig(
            api_key=api_key,
            model="qwen-plus-latest",
            temperature=0.7,
            max_tokens=1000
        )
        
        # 生成策略配置
        self.generation_strategies = {
            "balanced": {
                "description": "均衡策略 - 质量与数量平衡",
                "sample_size": 300,
                "conversations_per_meal": 3,
                "expected_output": 900
            },
            "quality_focused": {
                "description": "质量优先 - 少而精",
                "sample_size": 150,
                "conversations_per_meal": 4,
                "expected_output": 600
            },
            "quantity_focused": {
                "description": "数量优先 - 大规模生成",
                "sample_size": 500,
                "conversations_per_meal": 2,
                "expected_output": 1000
            },
            "custom": {
                "description": "自定义策略",
                "sample_size": 200,
                "conversations_per_meal": 3,
                "expected_output": 600
            }
        }
    
    def load_and_sample_data(self, strategy: str = "hybrid", target_size: int = 300) -> pd.DataFrame:
        """加载并智能采样数据"""
        print("🔄 加载NutriBench数据集...")
        
        try:
            df = pd.read_parquet('NutriBench/v2/train-00000-of-00001.parquet')
            print(f"✅ 成功加载: {len(df):,} 条数据")
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return pd.DataFrame()
        
        # 智能采样
        print(f"\n🎯 开始智能采样 (策略: {strategy}, 目标: {target_size} 条)...")
        sampler = SmartDataGenerationStrategy(df)
        selected_data = sampler.generate_smart_sample(strategy=strategy, target_size=target_size)
        
        return selected_data
    
    def generate_conversations_batch(self, meal_data_batch: List[Dict],
                                   conversations_per_meal: int = 3,
                                   use_parallel: bool = True,
                                   max_workers: int = 5) -> List[Dict[str, Any]]:
        """批量生成对话（支持并行处理）"""
        print(f"\n🤖 开始批量生成对话...")
        print(f"📊 餐食数量: {len(meal_data_batch)}")
        print(f"📊 每餐对话数: {conversations_per_meal}")
        print(f"📊 预期总对话数: {len(meal_data_batch) * conversations_per_meal}")
        print(f"⚡ 并行处理: {'启用' if use_parallel else '禁用'}")

        conversation_types = [
            "nutrition_analysis", "feeding_guidance", "texture_advice",
            "portion_guidance", "safety_advice", "problem_solving"
        ]

        # 准备所有任务
        tasks = []
        for meal_data in meal_data_batch:
            for _ in range(conversations_per_meal):
                age_group = self._smart_age_selection(meal_data)
                conv_type = random.choice(conversation_types)
                tasks.append((meal_data, age_group, conv_type))

        if use_parallel and self.enable_parallel:
            # 使用并行处理
            parallel_builder = ParallelQwenBuilder(self.qwen_config, max_workers=max_workers or self.max_workers)
            all_conversations = parallel_builder.batch_generate_parallel(tasks)
        else:
            # 使用原来的串行处理
            all_conversations = self._generate_conversations_serial(tasks)

        return all_conversations

    def _generate_conversations_serial(self, tasks: List[tuple]) -> List[Dict[str, Any]]:
        """串行生成对话（原来的方法）"""
        print(f"🐌 使用串行处理模式...")

        builder = EnhancedQwenBuilder(self.qwen_config)
        all_conversations = []
        successful_count = 0
        failed_count = 0

        for i, (meal_data, age_group, conv_type) in enumerate(tasks):
            print(f"📝 处理任务 {i+1}/{len(tasks)}: {conv_type} ({age_group})")

            try:
                conversation = builder.generate_enhanced_conversation(
                    meal_data, age_group, conv_type
                )

                if conversation:
                    all_conversations.append(conversation)
                    successful_count += 1
                    print(f"  ✅ 成功")
                else:
                    failed_count += 1
                    print(f"  ❌ 失败")

                # API限制延迟
                time.sleep(1.0)

            except Exception as e:
                failed_count += 1
                print(f"  ❌ 异常: {str(e)}")
                time.sleep(2.0)

        success_rate = successful_count / len(tasks) * 100 if len(tasks) > 0 else 0
        print(f"\n🎉 串行生成完成!")
        print(f"📊 成功: {successful_count}, 失败: {failed_count}, 成功率: {success_rate:.1f}%")

        return all_conversations
    
    def _smart_age_selection(self, meal_data: Dict) -> str:
        """智能选择年龄组"""
        energy = meal_data.get('energy', 0)
        protein = meal_data.get('protein', 0)
        
        # 基于营养特征智能选择年龄组
        if energy < 100 and protein < 5:
            # 低能量低蛋白，适合早期辅食
            return random.choice(["6m_start", "6-9m"])
        elif energy > 400 or protein > 25:
            # 高能量高蛋白，适合较大婴幼儿
            return random.choice(["12-24m"])
        elif 200 <= energy <= 400 and 10 <= protein <= 20:
            # 中等营养，适合中期辅食
            return random.choice(["9-12m", "12-24m"])
        else:
            # 随机选择
            return random.choice(["6m_start", "6-9m", "9-12m", "12-24m"])
    
    def generate_efficient_dataset(self, strategy_name: str = "balanced") -> Dict[str, Any]:
        """高效生成数据集"""
        print("🚀 高效数据集生成器启动")
        print("=" * 70)
        
        if strategy_name not in self.generation_strategies:
            print(f"❌ 不支持的策略: {strategy_name}")
            return {}
        
        strategy = self.generation_strategies[strategy_name]
        print(f"📋 选择策略: {strategy['description']}")
        print(f"📊 采样目标: {strategy['sample_size']} 条餐食")
        print(f"📊 每餐对话: {strategy['conversations_per_meal']} 个")
        print(f"📊 预期输出: {strategy['expected_output']} 条对话")
        
        # 第一步：智能采样
        print(f"\n{'='*70}")
        print("第一步：智能数据采样")
        print(f"{'='*70}")
        
        selected_data = self.load_and_sample_data(
            strategy="hybrid", 
            target_size=strategy['sample_size']
        )
        
        if selected_data.empty:
            print("❌ 数据采样失败")
            return {}
        
        # 第二步：批量生成对话
        print(f"\n{'='*70}")
        print("第二步：批量生成对话")
        print(f"{'='*70}")
        
        meal_data_list = selected_data.to_dict('records')
        conversations = self.generate_conversations_batch(
            meal_data_list,
            strategy['conversations_per_meal'],
            use_parallel=self.enable_parallel,
            max_workers=self.max_workers
        )
        
        # 第三步：保存结果
        print(f"\n{'='*70}")
        print("第三步：保存生成结果")
        print(f"{'='*70}")
        
        timestamp = int(time.time())
        dataset_filename = f"efficient_dataset_{strategy_name}_{timestamp}.json"
        sample_filename = f"selected_samples_{strategy_name}_{timestamp}.parquet"
        
        # 保存对话数据集
        with open(dataset_filename, 'w', encoding='utf-8') as f:
            json.dump(conversations, f, ensure_ascii=False, indent=2)
        
        # 保存采样数据
        selected_data.to_parquet(sample_filename)
        
        # 生成统计报告
        stats = self._generate_statistics(conversations, selected_data, strategy)
        stats_filename = f"generation_stats_{strategy_name}_{timestamp}.json"
        
        with open(stats_filename, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"💾 对话数据集: {dataset_filename}")
        print(f"💾 采样数据: {sample_filename}")
        print(f"💾 统计报告: {stats_filename}")
        
        # 显示最终统计
        print(f"\n🎉 高效生成完成!")
        print(f"📊 实际生成对话: {len(conversations)} 条")
        print(f"📊 目标完成率: {len(conversations)/strategy['expected_output']*100:.1f}%")
        print(f"📊 数据利用率: {len(selected_data)/15617*100:.3f}%")
        
        return {
            "conversations": conversations,
            "selected_samples": selected_data,
            "statistics": stats,
            "files": {
                "dataset": dataset_filename,
                "samples": sample_filename,
                "stats": stats_filename
            }
        }
    
    def _generate_statistics(self, conversations: List[Dict], 
                           selected_data: pd.DataFrame, 
                           strategy: Dict) -> Dict[str, Any]:
        """生成详细统计"""
        if not conversations:
            return {}
        
        # 对话类型统计
        type_counts = {}
        age_counts = {}
        country_counts = {}
        
        for conv in conversations:
            metadata = conv['metadata']
            
            conv_type = metadata['type']
            type_counts[conv_type] = type_counts.get(conv_type, 0) + 1
            
            age_group = metadata['age_group']
            age_counts[age_group] = age_counts.get(age_group, 0) + 1
            
            country = metadata.get('country', 'Unknown')
            country_counts[country] = country_counts.get(country, 0) + 1
        
        # 营养数据统计
        nutrition_stats = {
            "energy": {
                "min": float(selected_data['energy'].min()),
                "max": float(selected_data['energy'].max()),
                "mean": float(selected_data['energy'].mean()),
                "std": float(selected_data['energy'].std())
            },
            "protein": {
                "min": float(selected_data['protein'].min()),
                "max": float(selected_data['protein'].max()),
                "mean": float(selected_data['protein'].mean()),
                "std": float(selected_data['protein'].std())
            }
        }
        
        return {
            "generation_summary": {
                "strategy": strategy['description'],
                "target_samples": strategy['sample_size'],
                "actual_samples": len(selected_data),
                "target_conversations": strategy['expected_output'],
                "actual_conversations": len(conversations),
                "completion_rate": len(conversations) / strategy['expected_output'],
                "efficiency_score": len(conversations) / len(selected_data)
            },
            "conversation_distribution": {
                "by_type": type_counts,
                "by_age_group": age_counts,
                "by_country": country_counts
            },
            "data_quality": {
                "countries_covered": selected_data['country'].nunique(),
                "nutrition_diversity": nutrition_stats,
                "avg_description_length": float(selected_data['meal_description'].str.len().mean())
            },
            "generation_metadata": {
                "model": "qwen-plus-latest",
                "portion_conversion_enabled": True,
                "official_guidelines_based": True,
                "generation_timestamp": int(time.time())
            }
        }

def main():
    """主函数"""
    print("⚡ 高效数据集生成器")
    print("=" * 70)
    
    # API密钥配置
    api_key = "sk-5eba46fbcff649d5bf28313bc865de10"  # 您的API密钥
    
    if not api_key:
        print("❌ 请配置API密钥")
        return
    
    # 并行处理配置
    print("⚡ 并行处理配置:")
    enable_parallel = input("启用并行处理? (y/n, 默认y): ").strip().lower()
    enable_parallel = enable_parallel != 'n'

    max_workers = 5
    if enable_parallel:
        workers_input = input("并行线程数 (1-10, 默认5): ").strip()
        if workers_input.isdigit():
            max_workers = max(1, min(10, int(workers_input)))

    print(f"🔧 配置: 并行处理={'启用' if enable_parallel else '禁用'}, 线程数={max_workers}")

    # 创建生成器
    generator = EfficientDatasetGenerator(
        api_key=api_key,
        max_workers=max_workers,
        enable_parallel=enable_parallel
    )

    # 显示可用策略
    print("\n📋 可用生成策略:")
    for name, strategy in generator.generation_strategies.items():
        print(f"  {name}: {strategy['description']}")
        print(f"    - 采样: {strategy['sample_size']} 条")
        print(f"    - 每餐对话: {strategy['conversations_per_meal']} 个")
        print(f"    - 预期输出: {strategy['expected_output']} 条")
        if enable_parallel:
            estimated_time = strategy['expected_output'] / max_workers / 60  # 估算时间(分钟)
            print(f"    - 预估时间: {estimated_time:.1f} 分钟 (并行)")
        print()

    # 选择策略
    strategy_choice = input("请选择生成策略 (balanced/quality_focused/quantity_focused/custom): ").strip()
    if not strategy_choice:
        strategy_choice = "balanced"

    if strategy_choice not in generator.generation_strategies:
        print(f"❌ 无效策略，使用默认策略: balanced")
        strategy_choice = "balanced"
    
    # 开始生成
    print(f"\n🚀 开始使用策略: {strategy_choice}")
    result = generator.generate_efficient_dataset(strategy_choice)
    
    if result:
        print(f"\n✅ 生成成功!")
        print(f"📁 生成文件:")
        for file_type, filename in result['files'].items():
            print(f"  {file_type}: {filename}")
    else:
        print(f"\n❌ 生成失败")

if __name__ == "__main__":
    main()
