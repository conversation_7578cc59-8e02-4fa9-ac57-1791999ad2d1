# NutriBench LLM微调数据集实施指南

## 项目概述

本项目基于NutriBench数据集，生成用于微调大语言模型的文本数据集，目标是构建一个支持**育幼健康监测-指导功能**的专业LLM，专注于**饮食多样性监测**方向。

## 已完成的工作

### 1. 数据集分析 ✅
- **NutriBench数据集结构分析**
  - v2版本：15,617样本，覆盖24个国家
  - 字段：meal_description, carb, protein, fat, energy, country, serving_type
  - 数据来源：WWEIA(美国) + FAO/WHO Gift(国际)

### 2. 营养指南知识库构建 ✅
- **权威指南整合**
  - 中国居民膳食指南(2022)
  - 托育机构婴幼儿喂养与营养指南
  - WHO婴幼儿喂养建议
  - 中国营养学会DRIs(2013)

- **年龄段营养需求标准**
  - 0-6个月：纯母乳喂养期
  - 6-12个月：辅食添加期
  - 1-3岁：幼儿期营养需求
  - 3-6岁：学龄前儿童营养

### 3. 多样化问答对生成系统 ✅
- **三种对话类型**
  - 营养分析类：详细营养成分分析和年龄适宜性评估
  - 饮食多样性监测类：食物多样性评估和膳食平衡建议
  - 健康指导类：个性化营养缺口分析和补充建议

- **核心功能特性**
  - 年龄段适配（6-12m, 1-3y, 3-6y）
  - 营养素比例分析
  - 食物多样性评估
  - 个性化建议生成
  - 安全性检查和注意事项

### 4. 数据质量验证系统 ✅
- **质量指标**
  - 指令重复率：0.0%
  - 格式一致性：100%
  - 关键词覆盖率：60-100%
  - 专业性验证：自动化检查

## 技术架构

### 核心组件

1. **数据生成器 (dataset_generator.py)**
   - 模板化对话生成
   - 营养知识库集成
   - 多年龄段适配
   - 批量数据处理

2. **营养指南知识库 (nutrition_guidelines.json)**
   - 结构化营养标准
   - 年龄段喂养原则
   - 安全性指导
   - 特殊情况处理

3. **数据质量分析器 (dataset_analyzer.py)**
   - 统计分析
   - 质量评估
   - 多样性检查
   - 专业性验证

### 数据格式

```json
{
  "instruction": "用户问题/指令",
  "output": "专业的营养指导回答",
  "metadata": {
    "type": "对话类型",
    "age_group": "年龄组",
    "country": "国家代码",
    "energy_level": "能量水平"
  }
}
```

## 使用方法

### 1. 环境准备
```bash
pip install pandas jinja2
```

### 2. 数据生成
```bash
python3 dataset_generator.py
```

### 3. 质量分析
```bash
python3 dataset_analyzer.py
```

## 扩展方案

### 1. 数据规模扩展
- **目标规模**：50,000+条训练数据
- **扩展策略**：
  - 下载完整NutriBench数据集
  - 增加更多对话模板
  - 引入数据增强技术

### 2. 功能增强
- **多轮对话支持**
- **图像识别集成**（餐食照片分析）
- **个性化推荐系统**
- **生长曲线监测**

### 3. 本土化优化
- **中国传统食物适配**
- **地方饮食文化集成**
- **节庆饮食指导**
- **特殊体质考虑**

## 微调建议

### 1. 模型选择
- **推荐模型**：ChatGLM3-6B, Qwen-7B, Baichuan2-7B
- **硬件要求**：至少16GB显存
- **训练时长**：预计2-4小时

### 2. 训练参数
```python
training_args = {
    "learning_rate": 2e-5,
    "batch_size": 4,
    "epochs": 3,
    "max_length": 512,
    "warmup_steps": 100
}
```

### 3. 评估指标
- **专业准确性**：营养建议的科学性
- **年龄适宜性**：建议与年龄段的匹配度
- **安全性**：潜在风险的识别能力
- **实用性**：家长的接受度和可操作性

## 部署建议

### 1. 应用场景
- **育儿APP集成**
- **智能硬件嵌入**
- **医疗机构辅助工具**
- **家庭营养顾问**

### 2. 安全考虑
- **免责声明**：不替代专业医疗建议
- **风险提示**：严重营养问题建议就医
- **数据隐私**：用户信息保护

### 3. 持续优化
- **用户反馈收集**
- **专家审核机制**
- **定期知识更新**
- **效果评估跟踪**

## 文件清单

- `nutribench_llm_dataset_plan.md` - 详细方案设计
- `dataset_generator.py` - 数据生成核心代码
- `nutrition_guidelines.json` - 营养指南知识库
- `dataset_analyzer.py` - 数据质量分析工具
- `nutribench_llm_dataset.json` - 生成的训练数据集
- `dataset_analysis_report.txt` - 数据质量分析报告
- `dataset_samples.json` - 数据样本示例

## 下一步计划

1. **数据规模扩展**：获取完整NutriBench数据，生成大规模训练集
2. **专业审核**：邀请营养专家审核数据质量
3. **模型微调**：使用生成的数据集进行LLM微调
4. **效果评估**：在真实场景中测试模型效果
5. **产品化**：开发用户友好的应用界面

## 联系方式

如需技术支持或合作，请联系项目团队。

---

*本项目遵循CC-BY-NC-SA-4.0许可证，仅供研究和非商业用途使用。*
